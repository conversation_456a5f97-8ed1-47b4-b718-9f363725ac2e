<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Pembayaran;
use App\Models\Pesanan;
use Illuminate\Http\Request;

class UserPembayaranController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Pembayaran::with('pesanan.pelanggan');

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('metode_pembayaran', 'like', "%{$search}%")
                  ->orWhere('status', 'like', "%{$search}%")
                  ->orWhereHas('pesanan.pelanggan', function($pelangganQuery) use ($search) {
                      $pelangganQuery->where('nama', 'like', "%{$search}%");
                  });
            });
        }

        $pembayarans = $query->latest()->paginate(10);

        return view('user.pembayarans.index', compact('pembayarans'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $pesanans = Pesanan::with('pelanggan')->get();
        return view('user.pembayarans.create', compact('pesanans'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'pesanan_id' => 'required|exists:pesanans,id',
            'jumlah_bayar' => 'required|numeric|min:0',
            'tanggal_bayar' => 'required|date',
            'metode_pembayaran' => 'required|in:cash,transfer,kartu_kredit,e_wallet',
            'status' => 'required|in:pending,lunas,gagal',
            'invoice_number' => 'required|string|unique:pembayarans,invoice_number',
            'catatan' => 'nullable|string',
        ]);

        Pembayaran::create($request->all());

        return redirect()->route('user.pembayarans.index')
                        ->with('success', 'Pembayaran berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Pembayaran $pembayaran)
    {
        $pembayaran->load('pesanan.pelanggan');
        return view('user.pembayarans.show', compact('pembayaran'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Pembayaran $pembayaran)
    {
        $pesanans = Pesanan::with('pelanggan')->get();
        return view('user.pembayarans.edit', compact('pembayaran', 'pesanans'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Pembayaran $pembayaran)
    {
        $request->validate([
            'pesanan_id' => 'required|exists:pesanans,id',
            'jumlah_bayar' => 'required|numeric|min:0',
            'tanggal_bayar' => 'required|date',
            'metode_pembayaran' => 'required|in:cash,transfer,kartu_kredit,e_wallet',
            'status' => 'required|in:pending,lunas,gagal',
            'invoice_number' => 'required|string|unique:pembayarans,invoice_number,' . $pembayaran->id,
            'catatan' => 'nullable|string',
        ]);

        $pembayaran->update($request->all());

        return redirect()->route('user.pembayarans.index')
                        ->with('success', 'Pembayaran berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Pembayaran $pembayaran)
    {
        $pembayaran->delete();

        return redirect()->route('user.pembayarans.index')
                        ->with('success', 'Pembayaran berhasil dihapus.');
    }
}
