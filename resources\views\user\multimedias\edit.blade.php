<x-user-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit File') }}
        </h2>
    </x-slot>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="card hover-lift">
            <div class="card-header p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 gradient-text">Edit File</h3>
                        <p class="text-gray-600 mt-1">{{ $multimedia->nama_file }}</p>
                    </div>
                    <a href="{{ route('user.multimedias.show', $multimedia) }}" class="btn btn-secondary px-6 py-3">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Form Card -->
        <div class="card">
            <div class="p-8">
                <form action="{{ route('user.multimedias.update', $multimedia) }}" method="POST" enctype="multipart/form-data" class="space-y-8">
                    @csrf
                    @method('PUT')
                    
                    <!-- Current File Preview -->
                    <div class="space-y-2">
                        <label class="form-label">File Saat Ini</label>
                        <div class="border-2 border-gray-200 rounded-lg p-4">
                            @if($multimedia->kategori == 'foto')
                                <img src="{{ asset('storage/' . $multimedia->path_file) }}" 
                                     alt="{{ $multimedia->nama_file }}"
                                     class="w-32 h-32 object-cover rounded-lg">
                            @elseif($multimedia->kategori == 'video')
                                <div class="w-32 h-32 bg-gray-900 rounded-lg flex items-center justify-center">
                                    <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1M9 10v5a2 2 0 002 2h2a2 2 0 002-2v-5"></path>
                                    </svg>
                                </div>
                            @else
                                <div class="w-32 h-32 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                                    <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                                    </svg>
                                </div>
                            @endif
                            <p class="text-sm text-gray-600 mt-2">{{ $multimedia->nama_file }}</p>
                        </div>
                    </div>

                    <!-- Replace File (Optional) -->
                    <div class="space-y-2">
                        <label for="path_file" class="form-label">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            Ganti File (Opsional)
                        </label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 transition-colors duration-200">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-600">
                                    <label for="path_file" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
                                        <span>Upload file baru</span>
                                        <input id="path_file" name="path_file" type="file" class="sr-only"
                                               accept="image/*,video/*,audio/*">
                                    </label>
                                    <p class="pl-1">atau drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500">Kosongkan jika tidak ingin mengganti file</p>
                            </div>
                        </div>
                        @error('path_file')
                            <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Pesanan -->
                        <div class="space-y-2">
                            <label for="pesanan_id" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Pesanan Terkait
                            </label>
                            <select id="pesanan_id" name="pesanan_id" required class="form-input w-full">
                                <option value="">Pilih Pesanan</option>
                                @foreach($pesanans as $pesanan)
                                    <option value="{{ $pesanan->id }}" {{ $multimedia->pesanan_id == $pesanan->id ? 'selected' : '' }}>
                                        {{ $pesanan->jenis_layanan }} - {{ $pesanan->pelanggan->nama }}
                                    </option>
                                @endforeach
                            </select>
                            @error('pesanan_id')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Nama File -->
                        <div class="space-y-2">
                            <label for="nama_file" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                                Nama File
                            </label>
                            <input type="text" id="nama_file" name="nama_file" value="{{ $multimedia->nama_file }}" required
                                   placeholder="Masukkan nama file yang mudah diingat"
                                   class="form-input w-full">
                            @error('nama_file')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Kategori -->
                        <div class="space-y-2">
                            <label for="kategori" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                                Kategori
                            </label>
                            <select id="kategori" name="kategori" required class="form-input w-full">
                                <option value="">Pilih Kategori</option>
                                <option value="preview" {{ $multimedia->kategori == 'preview' ? 'selected' : '' }}>Preview</option>
                                <option value="hasil_edit" {{ $multimedia->kategori == 'hasil_edit' ? 'selected' : '' }}>Hasil Edit</option>
                                <option value="raw" {{ $multimedia->kategori == 'raw' ? 'selected' : '' }}>RAW File</option>
                            </select>
                            @error('kategori')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Deskripsi -->
                    <div class="space-y-2">
                        <label for="deskripsi" class="form-label">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Deskripsi (Opsional)
                        </label>
                        <textarea id="deskripsi" name="deskripsi" rows="4" 
                                  placeholder="Tambahkan deskripsi atau catatan tentang file ini"
                                  class="form-input w-full resize-none">{{ $multimedia->deskripsi }}</textarea>
                        @error('deskripsi')
                            <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                        <a href="{{ route('user.multimedias.show', $multimedia) }}" 
                           class="btn btn-secondary px-8 py-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Batal
                        </a>
                        <button type="submit" class="btn btn-primary px-8 py-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Update File
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-user-sidebar-layout>
