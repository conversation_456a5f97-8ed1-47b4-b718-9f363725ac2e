<x-user-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Dashboard Pelanggan') }}
        </h2>
    </x-slot>

    <div class="space-y-6 fade-in">
        <!-- Welcome Card -->
        <div class="card hover-lift">
            <div class="card-header p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 gradient-text">Selamat Datang!</h3>
                        <p class="text-gray-600 mt-1">{{ Auth::user()->name }} - <PERSON><PERSON><PERSON> pesanan dan booking Anda</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-500">{{ now()->format('l, d F Y') }}</p>
                        <p class="text-lg font-semibold text-blue-600">{{ now()->format('H:i') }} WIB</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Pesan Layanan -->
            <div class="card hover-lift">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-lg font-semibold text-gray-900">Pesan Layanan</h4>
                            <p class="text-sm text-gray-600">Buat pesanan layanan foto baru</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="{{ route('user.pesanans.create') }}" class="btn btn-primary w-full">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Pesan Sekarang
                        </a>
                    </div>
                </div>
            </div>

            <!-- Booking Studio -->
            <div class="card hover-lift">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-lg font-semibold text-gray-900">Booking Studio</h4>
                            <p class="text-sm text-gray-600">Jadwalkan sesi pemotretan</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="{{ route('user.bookings.create') }}" class="btn btn-secondary w-full">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Booking Sekarang
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <!-- Total Pesanan -->
            <div class="card">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Total Pesanan</p>
                            <p class="text-2xl font-bold text-gray-900">{{ \App\Models\Pesanan::count() }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Booking -->
            <div class="card">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Total Booking</p>
                            <p class="text-2xl font-bold text-gray-900">{{ \App\Models\Booking::count() }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Pelanggan -->
            <div class="card">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Total Pelanggan</p>
                            <p class="text-2xl font-bold text-gray-900">{{ \App\Models\Pelanggan::count() }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Foto -->
            <div class="card">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Total Foto</p>
                            <p class="text-2xl font-bold text-gray-900">{{ \App\Models\Multimedia::count() }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Pesanan Terbaru -->
            <div class="card">
                <div class="card-header p-6">
                    <div class="flex items-center justify-between">
                        <h4 class="text-lg font-semibold text-gray-900">Pesanan Terbaru</h4>
                        <a href="{{ route('user.pesanans.index') }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">Lihat Semua</a>
                    </div>
                </div>
                <div class="p-6 pt-0">
                    @php
                        $recentPesanans = \App\Models\Pesanan::with('pelanggan')->latest()->take(3)->get();
                    @endphp
                    @if($recentPesanans->count() > 0)
                        <div class="space-y-3">
                            @foreach($recentPesanans as $pesanan)
                                <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium text-gray-900">{{ $pesanan->jenis_layanan }}</p>
                                        <p class="text-sm text-gray-600">{{ $pesanan->pelanggan->nama ?? 'N/A' }}</p>
                                    </div>
                                    <span class="text-xs px-2 py-1 rounded-full 
                                        @if($pesanan->status == 'selesai') bg-green-100 text-green-800
                                        @elseif($pesanan->status == 'proses') bg-yellow-100 text-yellow-800
                                        @else bg-blue-100 text-blue-800 @endif">
                                        {{ ucfirst($pesanan->status) }}
                                    </span>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <p class="text-gray-500">Belum ada pesanan</p>
                            <a href="{{ route('user.pesanans.create') }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">Buat pesanan pertama</a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Booking Terbaru -->
            <div class="card">
                <div class="card-header p-6">
                    <div class="flex items-center justify-between">
                        <h4 class="text-lg font-semibold text-gray-900">Booking Terbaru</h4>
                        <a href="{{ route('user.bookings.index') }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">Lihat Semua</a>
                    </div>
                </div>
                <div class="p-6 pt-0">
                    @php
                        $recentBookings = \App\Models\Booking::with('pelanggan')->latest()->take(3)->get();
                    @endphp
                    @if($recentBookings->count() > 0)
                        <div class="space-y-3">
                            @foreach($recentBookings as $booking)
                                <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium text-gray-900">{{ $booking->jenis_acara }}</p>
                                        <p class="text-sm text-gray-600">{{ $booking->tanggal_booking->format('d M Y') }} - {{ $booking->pelanggan->nama ?? 'N/A' }}</p>
                                    </div>
                                    <span class="text-xs px-2 py-1 rounded-full 
                                        @if($booking->status == 'confirmed') bg-green-100 text-green-800
                                        @elseif($booking->status == 'pending') bg-yellow-100 text-yellow-800
                                        @else bg-blue-100 text-blue-800 @endif">
                                        {{ ucfirst($booking->status) }}
                                    </span>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <p class="text-gray-500">Belum ada booking</p>
                            <a href="{{ route('user.bookings.create') }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">Buat booking pertama</a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-user-sidebar-layout>
