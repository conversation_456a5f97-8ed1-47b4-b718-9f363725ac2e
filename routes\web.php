<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\PelangganController;
use App\Http\Controllers\PesananController;
use App\Http\Controllers\PemotretanController;
use App\Http\Controllers\PembayaranController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\MultimediaController;
use App\Http\Controllers\StokController;
use App\Http\Controllers\KaryawanController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('login');
});

// Dashboard routes with role-based access
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', function () {
        if (auth()->user()->role === 'admin') {
            return redirect()->route('dashboard.admin');
        }
        return redirect()->route('dashboard.user');
    })->name('dashboard');

    Route::get('/dashboard/admin', [DashboardController::class, 'admin'])
        ->middleware('role:admin')->name('dashboard.admin');

    Route::get('/dashboard/user', [DashboardController::class, 'user'])
        ->middleware('role:user')->name('dashboard.user');
});

// Admin-only routes - Full CRUD access
Route::middleware(['auth', 'role:admin'])->group(function () {
    Route::resource('pelanggans', PelangganController::class);
    Route::resource('pesanans', PesananController::class);
    Route::resource('pemotretans', PemotretanController::class);
    Route::resource('pembayarans', PembayaranController::class);
    Route::resource('bookings', BookingController::class);
    Route::resource('multimedias', MultimediaController::class);
    Route::resource('stoks', StokController::class);
    Route::resource('karyawans', KaryawanController::class);
    Route::resource('templates', TemplateController::class);
});

// User routes - Full CRUD access
Route::middleware(['auth', 'role:user'])->prefix('user')->name('user.')->group(function () {
    Route::get('/dashboard', [UserController::class, 'dashboard'])->name('dashboard');

    // User Pelanggan CRUD
    Route::resource('pelanggans', \App\Http\Controllers\User\UserPelangganController::class);

    // User Pesanan CRUD
    Route::resource('pesanans', \App\Http\Controllers\User\UserPesananController::class);

    // User Booking CRUD
    Route::resource('bookings', \App\Http\Controllers\User\UserBookingController::class);
});

// Profile routes for authenticated users
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
