<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Multimedia extends Model
{
    use HasFactory;

    protected $fillable = [
        'pesanan_id',
        'nama_file',
        'path_file',
        'tipe_file',
        'ukuran_file',
        'kategori',
        'deskripsi',
    ];

    // Relationships
    public function pesanan()
    {
        return $this->belongsTo(Pesanan::class);
    }

    // Accessor for file URL
    public function getFileUrlAttribute()
    {
        return asset('storage/' . $this->path_file);
    }
}
