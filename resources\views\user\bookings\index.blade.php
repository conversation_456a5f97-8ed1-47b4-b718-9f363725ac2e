<x-user-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Booking Studio Foto') }}
        </h2>
    </x-slot>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg">
            <div class="p-6 text-white">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold">📅 Booking Studio Saya</h3>
                        <p class="text-blue-100 mt-1">Kelola dan lihat status booking studio foto Anda</p>
                    </div>
                    <a href="{{ route('bookings.create') }}" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all duration-200">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Booking Baru
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content Card -->
        <div class="card">
            <div class="p-6">
                <!-- Quick Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
                        <div class="flex items-center">
                            <div class="bg-blue-100 p-3 rounded-full">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-600">Total Booking</p>
                                <p class="text-2xl font-bold text-gray-900">{{ $bookings->total() }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
                        <div class="flex items-center">
                            <div class="bg-yellow-100 p-3 rounded-full">
                                <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-600">Menunggu</p>
                                <p class="text-2xl font-bold text-yellow-600">{{ $bookings->where('status', 'pending')->count() }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
                        <div class="flex items-center">
                            <div class="bg-green-100 p-3 rounded-full">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-600">Dikonfirmasi</p>
                                <p class="text-2xl font-bold text-green-600">{{ $bookings->where('status', 'confirmed')->count() }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
                        <div class="flex items-center">
                            <div class="bg-purple-100 p-3 rounded-full">
                                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-600">Favorit</p>
                                <p class="text-2xl font-bold text-purple-600">Studio Wedding</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Booking Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @forelse($bookings as $booking)
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 border border-gray-100">
                            <!-- Card Header -->
                            <div class="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 p-6 text-white">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="font-bold text-xl mb-1">📸 {{ $booking->ruang_studio }}</h3>
                                        <p class="text-blue-100">{{ $booking->jenis_acara }}</p>
                                        <div class="flex items-center mt-2 text-sm text-blue-100">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                            {{ $booking->tanggal_booking->format('d M Y') }}
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-xs font-medium">
                                            #{{ $booking->id }}
                                        </span>
                                        <div class="mt-2">
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                @if($booking->status == 'confirmed') bg-green-500 text-white
                                                @elseif($booking->status == 'pending') bg-yellow-500 text-white
                                                @elseif($booking->status == 'cancelled') bg-red-500 text-white
                                                @else bg-gray-500 text-white @endif">
                                                @if($booking->status == 'confirmed') ✅ Dikonfirmasi
                                                @elseif($booking->status == 'pending') ⏳ Menunggu
                                                @elseif($booking->status == 'cancelled') ❌ Dibatalkan
                                                @else {{ ucfirst($booking->status) }} @endif
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Card Body -->
                            <div class="p-6">
                                <!-- Booking Info -->
                                <div class="grid grid-cols-2 gap-4 mb-6">
                                    <div class="text-center p-4 bg-gray-50 rounded-xl">
                                        <div class="text-2xl font-bold text-gray-900">{{ $booking->jam_mulai }}</div>
                                        <div class="text-sm text-gray-500">Jam Mulai</div>
                                    </div>
                                    <div class="text-center p-4 bg-gray-50 rounded-xl">
                                        <div class="text-2xl font-bold text-gray-900">{{ $booking->jumlah_peserta }}</div>
                                        <div class="text-sm text-gray-500">Orang</div>
                                    </div>
                                </div>

                                <!-- Customer Info -->
                                <div class="bg-blue-50 rounded-xl p-4 mb-6">
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-4">
                                            <span class="text-white font-bold text-lg">{{ substr($booking->pelanggan->nama, 0, 1) }}</span>
                                        </div>
                                        <div>
                                            <p class="font-semibold text-gray-900">{{ $booking->pelanggan->nama }}</p>
                                            <p class="text-sm text-gray-600">📱 {{ $booking->pelanggan->kontak }}</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="space-y-3">
                                    <button onclick="openMessageModal({{ $booking->id }}, '{{ $booking->ruang_studio }}')"
                                            class="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white py-3 px-4 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl">
                                        💬 Kirim Pesan
                                    </button>

                                    <div class="grid grid-cols-2 gap-3">
                                        <a href="{{ route('bookings.show', $booking) }}"
                                           class="bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors duration-200">
                                            👁️ Detail
                                        </a>
                                        @if($booking->status == 'pending')
                                            <a href="{{ route('bookings.edit', $booking) }}"
                                               class="bg-yellow-600 hover:bg-yellow-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors duration-200">
                                                ✏️ Edit
                                            </a>
                                        @else
                                            <div class="bg-gray-200 text-gray-500 text-center py-2 px-4 rounded-lg font-medium">
                                                🔒 Terkunci
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-span-full text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Belum ada booking</h3>
                            <p class="mt-1 text-sm text-gray-500">Mulai dengan membuat booking studio foto pertama Anda.</p>
                            <div class="mt-6">
                                <a href="{{ route('bookings.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    📅 Booking Sekarang
                                </a>
                            </div>
                        </div>
                    @endforelse
                </div>

                <!-- Pagination -->
                @if($bookings->hasPages())
                    <div class="mt-6">
                        {{ $bookings->links() }}
                    </div>
                @endif
            </div>
        </div>

        <!-- Message Modal -->
        <div id="messageModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl max-w-lg w-full max-h-[80vh] overflow-hidden shadow-2xl">
                <!-- Modal Header -->
                <div class="bg-gradient-to-r from-green-600 to-blue-600 p-6 text-white">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-xl font-bold" id="messageModalTitle">💬 Pesan Booking</h3>
                            <p class="text-green-100 text-sm" id="messageModalSubtitle">Kirim pesan terkait booking Anda</p>
                        </div>
                        <button onclick="closeMessageModal()" class="text-white hover:text-gray-200 bg-white bg-opacity-20 hover:bg-opacity-30 p-2 rounded-full transition-all duration-200">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Messages Area -->
                <div class="h-64 overflow-y-auto p-4 bg-gray-50" id="messagesArea">
                    <div class="text-center text-gray-500 py-8">
                        <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        Memuat pesan...
                    </div>
                </div>

                <!-- Message Form -->
                <form id="messageForm" class="p-4 border-t border-gray-200">
                    <div class="flex space-x-3">
                        <input type="hidden" id="bookingId" name="booking_id">
                        <textarea id="messageInput" name="message" rows="2" required
                                  placeholder="Ketik pesan Anda di sini..."
                                  class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 resize-none"></textarea>
                        <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                            📤
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        let currentBookingId = null;

        function openMessageModal(bookingId, studioName) {
            currentBookingId = bookingId;
            document.getElementById('messageModal').classList.remove('hidden');
            document.getElementById('messageModalTitle').textContent = '💬 Pesan Booking';
            document.getElementById('messageModalSubtitle').textContent = 'Booking ' + studioName;
            document.getElementById('bookingId').value = bookingId;
            document.body.style.overflow = 'hidden';

            loadMessages(bookingId);
        }

        function closeMessageModal() {
            document.getElementById('messageModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
            document.getElementById('messageForm').reset();
            currentBookingId = null;
        }

        function loadMessages(bookingId) {
            fetch(`/bookings/${bookingId}/messages`)
                .then(response => response.json())
                .then(messages => {
                    const messagesArea = document.getElementById('messagesArea');
                    if (messages.length === 0) {
                        messagesArea.innerHTML = `
                            <div class="text-center text-gray-500 py-8">
                                <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                                <p>Belum ada pesan</p>
                                <p class="text-sm">Mulai percakapan dengan mengirim pesan</p>
                            </div>
                        `;
                    } else {
                        messagesArea.innerHTML = messages.map(message => `
                            <div class="mb-4 ${message.sender_type === 'customer' ? 'text-right' : 'text-left'}">
                                <div class="inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                                    message.sender_type === 'customer'
                                        ? 'bg-blue-600 text-white'
                                        : 'bg-white text-gray-900 border border-gray-200'
                                }">
                                    <p class="text-sm">${message.message}</p>
                                    <p class="text-xs mt-1 ${message.sender_type === 'customer' ? 'text-blue-100' : 'text-gray-500'}">
                                        ${new Date(message.created_at).toLocaleString('id-ID')}
                                    </p>
                                </div>
                            </div>
                        `).join('');
                        messagesArea.scrollTop = messagesArea.scrollHeight;
                    }
                })
                .catch(error => {
                    console.error('Error loading messages:', error);
                    document.getElementById('messagesArea').innerHTML = `
                        <div class="text-center text-red-500 py-8">
                            <p>Gagal memuat pesan</p>
                        </div>
                    `;
                });
        }

        // Handle message form submission
        document.getElementById('messageForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const bookingId = document.getElementById('bookingId').value;

            fetch(`/bookings/${bookingId}/messages`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success !== false) {
                    document.getElementById('messageInput').value = '';
                    loadMessages(bookingId);
                }
            })
            .catch(error => {
                console.error('Error sending message:', error);
            });
        });

        // Close modal when clicking outside
        document.getElementById('messageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeMessageModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMessageModal();
            }
        });
    </script>
</x-user-sidebar-layout>
