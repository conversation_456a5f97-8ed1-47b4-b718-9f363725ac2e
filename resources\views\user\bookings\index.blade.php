<x-user-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Booking Studio Foto') }}
        </h2>
    </x-slot>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg">
            <div class="p-6 text-white">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold">📅 Booking Studio Saya</h3>
                        <p class="text-blue-100 mt-1">Kelola dan lihat status booking studio foto Anda</p>
                    </div>
                    <a href="{{ route('bookings.create') }}" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all duration-200">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Booking Baru
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content Card -->
        <div class="card">
            <div class="p-6">
                <!-- Search Section -->
                <div class="mb-6">
                    <form method="GET" action="{{ route('bookings.index') }}" class="flex gap-4">
                        <div class="flex-1">
                            <input type="text" name="search" value="{{ request('search') }}"
                                   placeholder="Cari studio, jenis acara, atau nama pelanggan..."
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors duration-200">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                        @if(request('search'))
                            <a href="{{ route('bookings.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition-colors duration-200">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </a>
                        @endif
                    </form>
                </div>

                <!-- Booking Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @forelse($bookings as $booking)
                        <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 border border-gray-200">
                            <!-- Card Header -->
                            <div class="bg-gradient-to-r from-blue-500 to-purple-500 p-4 text-white">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <h3 class="font-bold text-lg">{{ $booking->ruang_studio }}</h3>
                                        <p class="text-blue-100 text-sm">{{ $booking->jenis_acara }}</p>
                                    </div>
                                    <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-xs font-medium">
                                        #{{ $booking->id }}
                                    </span>
                                </div>
                            </div>

                            <!-- Card Body -->
                            <div class="p-6">
                                <!-- Customer Info -->
                                <div class="flex items-center mb-4">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-white font-bold">{{ substr($booking->pelanggan->nama, 0, 1) }}</span>
                                    </div>
                                    <div>
                                        <p class="font-semibold text-gray-900">{{ $booking->pelanggan->nama }}</p>
                                        <p class="text-sm text-gray-500">{{ $booking->pelanggan->kontak }}</p>
                                    </div>
                                </div>

                                <!-- Booking Details -->
                                <div class="space-y-2 mb-4">
                                    <div class="flex items-center text-sm text-gray-600">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        {{ $booking->tanggal_booking->format('d M Y') }}
                                    </div>
                                    <div class="flex items-center text-sm text-gray-600">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        {{ $booking->jam_mulai }} - {{ $booking->jam_selesai }}
                                    </div>
                                    <div class="flex items-center text-sm text-gray-600">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                        </svg>
                                        {{ $booking->jumlah_peserta }} orang
                                    </div>
                                </div>

                                <!-- Status -->
                                <div class="mb-4">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                        @if($booking->status == 'confirmed') bg-green-100 text-green-800
                                        @elseif($booking->status == 'pending') bg-yellow-100 text-yellow-800
                                        @elseif($booking->status == 'cancelled') bg-red-100 text-red-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        @if($booking->status == 'confirmed') ✅ Dikonfirmasi
                                        @elseif($booking->status == 'pending') ⏳ Menunggu
                                        @elseif($booking->status == 'cancelled') ❌ Dibatalkan
                                        @else {{ ucfirst($booking->status) }} @endif
                                    </span>
                                </div>

                                <!-- Actions -->
                                <div class="flex space-x-2">
                                    <a href="{{ route('bookings.show', $booking) }}"
                                       class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-3 rounded-lg text-sm font-medium transition-colors duration-200">
                                        👁️ Detail
                                    </a>
                                    <button onclick="openMessageModal({{ $booking->id }}, '{{ $booking->ruang_studio }}')"
                                            class="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors duration-200">
                                        💬 Pesan
                                    </button>
                                    @if($booking->status == 'pending')
                                        <a href="{{ route('bookings.edit', $booking) }}"
                                           class="bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors duration-200">
                                            ✏️
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-span-full text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Belum ada booking</h3>
                            <p class="mt-1 text-sm text-gray-500">Mulai dengan membuat booking studio foto pertama Anda.</p>
                            <div class="mt-6">
                                <a href="{{ route('bookings.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    📅 Booking Sekarang
                                </a>
                            </div>
                        </div>
                    @endforelse
                </div>

                <!-- Pagination -->
                @if($bookings->hasPages())
                    <div class="mt-6">
                        {{ $bookings->links() }}
                    </div>
                @endif
            </div>
        </div>

        <!-- Message Modal -->
        <div id="messageModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl max-w-lg w-full max-h-[80vh] overflow-hidden shadow-2xl">
                <!-- Modal Header -->
                <div class="bg-gradient-to-r from-green-600 to-blue-600 p-6 text-white">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-xl font-bold" id="messageModalTitle">💬 Pesan Booking</h3>
                            <p class="text-green-100 text-sm" id="messageModalSubtitle">Kirim pesan terkait booking Anda</p>
                        </div>
                        <button onclick="closeMessageModal()" class="text-white hover:text-gray-200 bg-white bg-opacity-20 hover:bg-opacity-30 p-2 rounded-full transition-all duration-200">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Messages Area -->
                <div class="h-64 overflow-y-auto p-4 bg-gray-50" id="messagesArea">
                    <div class="text-center text-gray-500 py-8">
                        <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        Memuat pesan...
                    </div>
                </div>

                <!-- Message Form -->
                <form id="messageForm" class="p-4 border-t border-gray-200">
                    <div class="flex space-x-3">
                        <input type="hidden" id="bookingId" name="booking_id">
                        <textarea id="messageInput" name="message" rows="2" required
                                  placeholder="Ketik pesan Anda di sini..."
                                  class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 resize-none"></textarea>
                        <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                            📤
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        let currentBookingId = null;

        function openMessageModal(bookingId, studioName) {
            currentBookingId = bookingId;
            document.getElementById('messageModal').classList.remove('hidden');
            document.getElementById('messageModalTitle').textContent = '💬 Pesan Booking';
            document.getElementById('messageModalSubtitle').textContent = 'Booking ' + studioName;
            document.getElementById('bookingId').value = bookingId;
            document.body.style.overflow = 'hidden';

            loadMessages(bookingId);
        }

        function closeMessageModal() {
            document.getElementById('messageModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
            document.getElementById('messageForm').reset();
            currentBookingId = null;
        }

        function loadMessages(bookingId) {
            fetch(`/bookings/${bookingId}/messages`)
                .then(response => response.json())
                .then(messages => {
                    const messagesArea = document.getElementById('messagesArea');
                    if (messages.length === 0) {
                        messagesArea.innerHTML = `
                            <div class="text-center text-gray-500 py-8">
                                <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                                <p>Belum ada pesan</p>
                                <p class="text-sm">Mulai percakapan dengan mengirim pesan</p>
                            </div>
                        `;
                    } else {
                        messagesArea.innerHTML = messages.map(message => `
                            <div class="mb-4 ${message.sender_type === 'customer' ? 'text-right' : 'text-left'}">
                                <div class="inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                                    message.sender_type === 'customer'
                                        ? 'bg-blue-600 text-white'
                                        : 'bg-white text-gray-900 border border-gray-200'
                                }">
                                    <p class="text-sm">${message.message}</p>
                                    <p class="text-xs mt-1 ${message.sender_type === 'customer' ? 'text-blue-100' : 'text-gray-500'}">
                                        ${new Date(message.created_at).toLocaleString('id-ID')}
                                    </p>
                                </div>
                            </div>
                        `).join('');
                        messagesArea.scrollTop = messagesArea.scrollHeight;
                    }
                })
                .catch(error => {
                    console.error('Error loading messages:', error);
                    document.getElementById('messagesArea').innerHTML = `
                        <div class="text-center text-red-500 py-8">
                            <p>Gagal memuat pesan</p>
                        </div>
                    `;
                });
        }

        // Handle message form submission
        document.getElementById('messageForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const bookingId = document.getElementById('bookingId').value;

            fetch(`/bookings/${bookingId}/messages`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success !== false) {
                    document.getElementById('messageInput').value = '';
                    loadMessages(bookingId);
                }
            })
            .catch(error => {
                console.error('Error sending message:', error);
            });
        });

        // Close modal when clicking outside
        document.getElementById('messageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeMessageModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMessageModal();
            }
        });
    </script>
</x-user-sidebar-layout>
