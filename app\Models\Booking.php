<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Booking extends Model
{
    use HasFactory;

    protected $fillable = [
        'pelanggan_id',
        'tanggal_booking',
        'jam_mulai',
        'jam_selesai',
        'ruang_studio',
        'jenis_acara',
        'jumlah_peserta',
        'durasi',
        'status',
        'catatan',
    ];

    protected $casts = [
        'tanggal_booking' => 'date',
        'jam_mulai' => 'datetime:H:i',
        'jam_selesai' => 'datetime:H:i',
    ];

    // Relationships
    public function pelanggan()
    {
        return $this->belongsTo(Pelanggan::class);
    }

    public function messages()
    {
        return $this->hasMany(BookingMessage::class);
    }

    public function latestMessage()
    {
        return $this->hasOne(BookingMessage::class)->latest();
    }

    public function unreadMessages()
    {
        return $this->hasMany(BookingMessage::class)->where('is_read', false);
    }
}
