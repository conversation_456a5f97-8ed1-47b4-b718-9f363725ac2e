# TokoPhotoStudio - Sistem Manajemen Studio Foto

Sistem manajemen lengkap untuk usaha studio foto yang dibangun dengan Laravel 12 dan <PERSON><PERSON>ze untuk autentikasi.

## 🚀 Fitur Utama

### Autentikasi & Role Management
- **Login & Logout** untuk dua role: Admin dan User
- **Role-based Access Control**:
  - User role: hanya bisa mengakses `/dashboard/user`
  - Admin role: bisa mengakses semua dashboard dan semua fitur CRUD
- **Dashboard terpisah** untuk admin dan user

### CRUD Lengkap untuk Entitas
1. **Data Pelanggan** - nama, kontak, email, riwayat transaksi
2. **Data Pesanan Foto** - jenis layanan, paket, harga, tanggal
3. **Riwayat Pemotretan** - tanggal, lokasi, fotografer, jenis foto
4. **Data Pembayaran & Invoice** - jumlah, metode, status, invoice number
5. **Jadwal Booking Studio** - tanggal, jam, ruang studio, status
6. **File Multimedia** - preview foto, hasil edit dengan upload ke `storage/app/public`
7. **Stok Barang** - cetak foto, frame, album, dll
8. **Data Karyawan** - fotografer, editor, admin
9. **Template & Preset Editing** - template dengan preset settings

### Fitur Tambahan
- ✅ **Search bar** pada semua halaman CRUD
- ✅ **Validasi input** pada semua form
- ✅ **File upload** untuk multimedia ke `storage/app/public`
- ✅ **Pagination** untuk semua listing
- ✅ **Responsive design** dengan Tailwind CSS

## 🛠 Teknologi yang Digunakan

- **Laravel 12** - Framework PHP
- **Laravel Breeze** - Autentikasi
- **SQLite** - Database
- **Tailwind CSS** - Styling
- **Blade Templates** - View Engine

## 📦 Instalasi & Setup

### 1. Install Dependencies
```bash
composer install
npm install && npm run build
```

### 2. Environment Setup
```bash
cp .env.example .env
php artisan key:generate
```

### 3. Database Setup
```bash
php artisan migrate:fresh --seed
php artisan storage:link
```

### 4. Jalankan Server
```bash
php artisan serve
```

Akses aplikasi di: `http://127.0.0.1:8000`

## 👤 Default User Accounts

### Admin Account
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Role**: Admin (akses penuh)

### User Account
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Role**: User (akses terbatas)

## 📁 Struktur Proyek

```
TokoPhotoStudio/
├── app/
│   ├── Http/
│   │   ├── Controllers/          # Resource Controllers
│   │   └── Middleware/           # Role Middleware
│   └── Models/                   # Eloquent Models
├── database/
│   ├── migrations/               # Database Migrations
│   └── seeders/                  # Database Seeders
├── resources/
│   └── views/                    # Blade Templates
│       ├── dashboard/            # Admin & User Dashboards
│       ├── pelanggans/           # Pelanggan CRUD Views
│       ├── pesanans/             # Pesanan CRUD Views
│       └── ...                   # Other CRUD Views
├── routes/
│   └── web.php                   # Web Routes with Middleware
└── storage/
    └── app/public/               # File Upload Storage
```

## 🔐 Role-based Access Control

### Admin Role
- Akses ke semua fitur CRUD
- Dashboard admin dengan statistik
- Manajemen semua data

### User Role
- Akses terbatas ke dashboard user
- Tidak bisa mengakses fitur CRUD
- Hanya bisa melihat informasi akun

## 📊 Database Schema

### Users Table
- id, name, email, password, role, timestamps

### Pelanggans Table
- id, nama, kontak, email, alamat, timestamps

### Pesanans Table
- id, pelanggan_id, jenis_layanan, paket, harga, tanggal_pesanan, status, catatan, timestamps

### Dan 6 tabel lainnya dengan relasi yang tepat...

## 🚦 Testing

Untuk menguji aplikasi:

1. **Login sebagai Admin**:
   - Email: `<EMAIL>`
   - Password: `admin123`
   - Test semua fitur CRUD

2. **Login sebagai User**:
   - Email: `<EMAIL>`
   - Password: `user123`
   - Verifikasi akses terbatas

## 📝 Catatan Pengembangan

Proyek ini dibuat untuk tugas UAS Semester 2 Teknik Informatika dengan fokus pada:
- Implementasi Laravel best practices
- Role-based access control
- CRUD operations yang lengkap
- File upload management
- Responsive UI/UX design

## 🤝 Kontribusi

Proyek ini adalah tugas akademik. Untuk pengembangan lebih lanjut, silakan fork repository dan buat pull request.

## 📄 Lisensi

Proyek ini menggunakan lisensi MIT.
