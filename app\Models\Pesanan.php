<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Pesanan extends Model
{
    use HasFactory;

    protected $fillable = [
        'pelanggan_id',
        'layanan_id',
        'jenis_layanan',
        'paket',
        'harga',
        'tanggal_pesanan',
        'status',
        'catatan',
    ];

    protected $casts = [
        'tanggal_pesanan' => 'date',
        'harga' => 'decimal:2',
    ];

    // Relationships
    public function pelanggan()
    {
        return $this->belongsTo(Pelanggan::class);
    }

    public function layanan()
    {
        return $this->belongsTo(Layanan::class);
    }

    public function pemotretans()
    {
        return $this->hasMany(Pemotretan::class);
    }

    public function pembayarans()
    {
        return $this->hasMany(Pembayaran::class);
    }

    public function multimedias()
    {
        return $this->hasMany(Multimedia::class);
    }
}
