<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Pemotretan;
use App\Models\Pesanan;
use App\Models\Karyawan;
use Illuminate\Http\Request;

class UserPemotretanController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Pemotretan::with(['pesanan.pelanggan', 'karyawan']);

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('lokasi', 'like', "%{$search}%")
                  ->orWhere('jenis_foto', 'like', "%{$search}%")
                  ->orWhereHas('pesanan.pelanggan', function($pelangganQuery) use ($search) {
                      $pelangganQuery->where('nama', 'like', "%{$search}%");
                  });
            });
        }

        $pemotretans = $query->latest()->paginate(10);

        return view('user.pemotretans.index', compact('pemotretans'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $pesanans = Pesanan::with('pelanggan')->get();
        $karyawans = Karyawan::where('posisi', 'fotografer')->where('status', 'aktif')->get();
        return view('user.pemotretans.create', compact('pesanans', 'karyawans'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'pesanan_id' => 'required|exists:pesanans,id',
            'karyawan_id' => 'required|exists:karyawans,id',
            'tanggal_pemotretan' => 'required|date',
            'lokasi' => 'required|string|max:255',
            'jenis_foto' => 'required|string|max:255',
            'durasi' => 'required|integer|min:1',
            'status' => 'required|in:terjadwal,berlangsung,selesai,dibatalkan',
            'catatan' => 'nullable|string',
        ]);

        Pemotretan::create($request->all());

        return redirect()->route('user.pemotretans.index')
                        ->with('success', 'Pemotretan berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Pemotretan $pemotretan)
    {
        $pemotretan->load(['pesanan.pelanggan', 'karyawan']);
        return view('user.pemotretans.show', compact('pemotretan'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Pemotretan $pemotretan)
    {
        $pesanans = Pesanan::with('pelanggan')->get();
        $karyawans = Karyawan::where('posisi', 'fotografer')->where('status', 'aktif')->get();
        return view('user.pemotretans.edit', compact('pemotretan', 'pesanans', 'karyawans'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Pemotretan $pemotretan)
    {
        $request->validate([
            'pesanan_id' => 'required|exists:pesanans,id',
            'karyawan_id' => 'required|exists:karyawans,id',
            'tanggal_pemotretan' => 'required|date',
            'lokasi' => 'required|string|max:255',
            'jenis_foto' => 'required|string|max:255',
            'durasi' => 'required|integer|min:1',
            'status' => 'required|in:terjadwal,berlangsung,selesai,dibatalkan',
            'catatan' => 'nullable|string',
        ]);

        $pemotretan->update($request->all());

        return redirect()->route('user.pemotretans.index')
                        ->with('success', 'Pemotretan berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Pemotretan $pemotretan)
    {
        $pemotretan->delete();

        return redirect()->route('user.pemotretans.index')
                        ->with('success', 'Pemotretan berhasil dihapus.');
    }
}
