<?php if (isset($component)) { $__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-sidebar-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('user-sidebar-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('User Dashboard')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="space-y-6 fade-in">
        <!-- Welcome Card -->
        <div class="card hover-lift">
                <div class="p-6 text-gray-900">
                    <div class="flex items-center">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-6">
                            <span class="text-white font-bold text-2xl"><?php echo e(substr(auth()->user()->name, 0, 1)); ?></span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">Selamat Datang, <?php echo e(auth()->user()->name); ?>!</h3>
                            <p class="text-gray-600 mt-1">Anda login sebagai <span class="font-semibold text-blue-600">User</span> - Akses terbatas untuk melihat data</p>
                        </div>
                    </div>
                </div>
            </div>

        <!-- User Info Card -->
        <div class="card">
                <div class="p-6 text-gray-900">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">Informasi Akun</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="flex items-center">
                                <svg class="w-8 h-8 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <div>
                                    <p class="text-sm text-gray-600">Nama</p>
                                    <p class="font-semibold text-gray-900"><?php echo e(auth()->user()->name); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="flex items-center">
                                <svg class="w-8 h-8 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                </svg>
                                <div>
                                    <p class="text-sm text-gray-600">Email</p>
                                    <p class="font-semibold text-gray-900"><?php echo e(auth()->user()->email); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <div class="flex items-center">
                                <svg class="w-8 h-8 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                                <div>
                                    <p class="text-sm text-gray-600">Role</p>
                                    <p class="font-semibold text-gray-900"><?php echo e(ucfirst(auth()->user()->role)); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <!-- Available Actions for User -->
        <div class="card">
                <div class="p-6 text-gray-900">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">Menu yang Tersedia (Read-Only)</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <a href="<?php echo e(route('user.pelanggans.index')); ?>" class="block p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200">
                            <div class="flex items-center">
                                <svg class="w-8 h-8 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                                <div>
                                    <h5 class="font-semibold text-gray-900">Lihat Data Pelanggan</h5>
                                    <p class="text-sm text-gray-600">Akses read-only</p>
                                </div>
                            </div>
                        </a>
                        <a href="<?php echo e(route('user.pesanans.index')); ?>" class="block p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors duration-200">
                            <div class="flex items-center">
                                <svg class="w-8 h-8 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <div>
                                    <h5 class="font-semibold text-gray-900">Lihat Data Pesanan</h5>
                                    <p class="text-sm text-gray-600">Akses read-only</p>
                                </div>
                            </div>
                        </a>
                        <a href="<?php echo e(route('user.bookings.index')); ?>" class="block p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors duration-200">
                            <div class="flex items-center">
                                <svg class="w-8 h-8 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <div>
                                    <h5 class="font-semibold text-gray-900">Lihat Jadwal Booking</h5>
                                    <p class="text-sm text-gray-600">Akses read-only</p>
                                </div>
                            </div>
                        </a>
                    </div>

                    <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-yellow-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <div>
                                <h5 class="font-semibold text-yellow-800">Akses Terbatas</h5>
                                <p class="text-sm text-yellow-700">Sebagai user, Anda hanya dapat melihat data. Untuk akses penuh (create, edit, delete), hubungi administrator untuk upgrade ke role admin.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14)): ?>
<?php $attributes = $__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14; ?>
<?php unset($__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14)): ?>
<?php $component = $__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14; ?>
<?php unset($__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\TokoPhotoStudio\resources\views/dashboard/user.blade.php ENDPATH**/ ?>