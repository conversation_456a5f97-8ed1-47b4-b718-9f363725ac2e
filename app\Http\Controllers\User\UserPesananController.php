<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Pesanan;
use App\Models\Pelanggan;
use Illuminate\Http\Request;

class UserPesananController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Pesanan::with('pelanggan');

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('jenis_layanan', 'like', "%{$search}%")
                  ->orWhere('deskripsi', 'like', "%{$search}%")
                  ->orWhereHas('pelanggan', function($pelangganQuery) use ($search) {
                      $pelangganQuery->where('nama', 'like', "%{$search}%");
                  });
            });
        }

        $pesanans = $query->latest()->paginate(10);

        return view('user.pesanans.index', compact('pesanans'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $pelanggans = Pelanggan::all();
        return view('user.pesanans.create', compact('pelanggans'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'pelanggan_id' => 'required|exists:pelanggans,id',
            'jenis_layanan' => 'required|string|max:255',
            'tanggal_pesanan' => 'required|date',
            'harga' => 'required|numeric|min:0',
            'status' => 'required|in:pending,proses,selesai,dibatalkan',
            'deskripsi' => 'nullable|string',
        ]);

        Pesanan::create($request->all());

        return redirect()->route('user.pesanans.index')
                        ->with('success', 'Pesanan berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Pesanan $pesanan)
    {
        $pesanan->load('pelanggan');
        return view('user.pesanans.show', compact('pesanan'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Pesanan $pesanan)
    {
        $pelanggans = Pelanggan::all();
        return view('user.pesanans.edit', compact('pesanan', 'pelanggans'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Pesanan $pesanan)
    {
        $request->validate([
            'pelanggan_id' => 'required|exists:pelanggans,id',
            'jenis_layanan' => 'required|string|max:255',
            'tanggal_pesanan' => 'required|date',
            'harga' => 'required|numeric|min:0',
            'status' => 'required|in:pending,proses,selesai,dibatalkan',
            'deskripsi' => 'nullable|string',
        ]);

        $pesanan->update($request->all());

        return redirect()->route('user.pesanans.index')
                        ->with('success', 'Pesanan berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Pesanan $pesanan)
    {
        $pesanan->delete();

        return redirect()->route('user.pesanans.index')
                        ->with('success', 'Pesanan berhasil dihapus.');
    }
}
