<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Pesanan;
use App\Models\Pelanggan;
use App\Models\Layanan;
use Illuminate\Http\Request;

class UserPesananController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Pesanan::with('pelanggan');

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('jenis_layanan', 'like', "%{$search}%")
                  ->orWhere('deskripsi', 'like', "%{$search}%")
                  ->orWhereHas('pelanggan', function($pelangganQuery) use ($search) {
                      $pelangganQuery->where('nama', 'like', "%{$search}%");
                  });
            });
        }

        $pesanans = $query->latest()->paginate(10);

        return view('user.pesanans.index', compact('pesanans'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $layanans = Layanan::aktif()->get();
        return view('user.pesanans.create', compact('layanans'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'layanan_id' => 'required|exists:layanans,id',
            'nama_pelanggan' => 'required|string|max:255',
            'email_pelanggan' => 'required|email|max:255',
            'kontak_pelanggan' => 'required|string|max:20',
            'alamat_pelanggan' => 'required|string',
            'tanggal_pesanan' => 'required|date',
            'paket_pilihan' => 'required|in:dasar,premium',
            'catatan' => 'nullable|string'
        ]);

        // Create or find pelanggan
        $pelanggan = Pelanggan::firstOrCreate(
            ['email' => $request->email_pelanggan],
            [
                'nama' => $request->nama_pelanggan,
                'kontak' => $request->kontak_pelanggan,
                'alamat' => $request->alamat_pelanggan,
            ]
        );

        // Get layanan and set harga
        $layanan = Layanan::findOrFail($request->layanan_id);
        $harga = $request->paket_pilihan === 'premium' && $layanan->harga_premium
                ? $layanan->harga_premium
                : $layanan->harga_dasar;

        $data = [
            'pelanggan_id' => $pelanggan->id,
            'layanan_id' => $request->layanan_id,
            'jenis_layanan' => $layanan->nama_layanan,
            'paket' => $request->paket_pilihan === 'premium' ? 'Premium Package' : 'Basic Package',
            'tanggal_pesanan' => $request->tanggal_pesanan,
            'harga' => $harga,
            'status' => 'pending',
            'catatan' => $request->catatan
        ];

        Pesanan::create($data);

        return redirect()->route('user.pesanans.index')
                        ->with('success', 'Pesanan berhasil dibuat! Kami akan menghubungi Anda segera.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Pesanan $pesanan)
    {
        $pesanan->load('pelanggan');
        return view('user.pesanans.show', compact('pesanan'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Pesanan $pesanan)
    {
        $pelanggans = Pelanggan::all();
        return view('user.pesanans.edit', compact('pesanan', 'pelanggans'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Pesanan $pesanan)
    {
        $request->validate([
            'pelanggan_id' => 'required|exists:pelanggans,id',
            'jenis_layanan' => 'required|string|max:255',
            'tanggal_pesanan' => 'required|date',
            'harga' => 'required|numeric|min:0',
            'status' => 'required|in:pending,proses,selesai,dibatalkan',
            'deskripsi' => 'nullable|string',
        ]);

        $pesanan->update($request->all());

        return redirect()->route('user.pesanans.index')
                        ->with('success', 'Pesanan berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Pesanan $pesanan)
    {
        $pesanan->delete();

        return redirect()->route('user.pesanans.index')
                        ->with('success', 'Pesanan berhasil dihapus.');
    }
}
