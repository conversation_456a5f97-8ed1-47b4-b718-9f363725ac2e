<?php

namespace App\Http\Controllers;

use App\Models\Pelanggan;
use App\Models\Pesanan;
use App\Models\Booking;
use Illuminate\Http\Request;

class UserController extends Controller
{
    /**
     * Display user dashboard
     */
    public function dashboard()
    {
        return view('user.dashboard');
    }

    /**
     * Display pelanggan for user (read-only)
     */
    public function pelanggans(Request $request)
    {
        $query = Pelanggan::query();
        
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where('nama', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('kontak', 'like', "%{$search}%");
        }
        
        $pelanggans = $query->paginate(10);
        
        return view('user.pelanggans.index', compact('pelanggans'));
    }

    /**
     * Show pelanggan detail for user (read-only)
     */
    public function showPelanggan(Pelanggan $pelanggan)
    {
        return view('user.pelanggans.show', compact('pelanggan'));
    }

    /**
     * Display pesanans for user (read-only)
     */
    public function pesanans(Request $request)
    {
        $query = Pesanan::with('pelanggan');
        
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where('jenis_layanan', 'like', "%{$search}%")
                  ->orWhereHas('pelanggan', function($q) use ($search) {
                      $q->where('nama', 'like', "%{$search}%");
                  });
        }
        
        $pesanans = $query->paginate(10);
        
        return view('user.pesanans.index', compact('pesanans'));
    }

    /**
     * Show pesanan detail for user (read-only)
     */
    public function showPesanan(Pesanan $pesanan)
    {
        $pesanan->load('pelanggan');
        return view('user.pesanans.show', compact('pesanan'));
    }

    /**
     * Display bookings for user (read-only)
     */
    public function bookings(Request $request)
    {
        $query = Booking::with('pelanggan');
        
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where('jenis_acara', 'like', "%{$search}%")
                  ->orWhere('lokasi', 'like', "%{$search}%")
                  ->orWhereHas('pelanggan', function($q) use ($search) {
                      $q->where('nama', 'like', "%{$search}%");
                  });
        }
        
        $bookings = $query->paginate(10);
        
        return view('user.bookings.index', compact('bookings'));
    }

    /**
     * Show booking detail for user (read-only)
     */
    public function showBooking(Booking $booking)
    {
        $booking->load('pelanggan');
        return view('user.bookings.show', compact('booking'));
    }
}
