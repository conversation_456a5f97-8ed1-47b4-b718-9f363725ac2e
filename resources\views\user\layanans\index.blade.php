<x-user-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Layanan Foto') }}
        </h2>
    </x-slot>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="card hover-lift">
            <div class="card-header p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 gradient-text">Layanan Foto Profesional</h3>
                        <p class="text-gray-600 mt-1">Pilih layanan foto yang sesuai dengan kebutuhan Anda</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{{ route('user.pesanans.create') }}" class="btn btn-primary px-6 py-3">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Pesan Sekarang
                        </a>
                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">{{ $layanans->total() }} Layanan</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search & Filter Card -->
        <div class="card">
            <div class="p-6">
                <form method="GET" action="{{ route('user.layanans.index') }}" class="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-4">
                    <div class="flex-1">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <input type="text" name="search" value="{{ request('search') }}" 
                                   placeholder="Cari layanan foto..."
                                   class="form-input pl-10 w-full">
                        </div>
                    </div>
                    <div class="w-full md:w-auto">
                        <select name="kategori" class="form-input w-full md:w-auto">
                            <option value="">Semua Kategori</option>
                            @foreach($kategoris as $kategori)
                                <option value="{{ $kategori }}" {{ request('kategori') == $kategori ? 'selected' : '' }}>
                                    {{ ucfirst($kategori) }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <button type="submit" class="btn btn-secondary px-6 py-3 w-full md:w-auto">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Cari
                    </button>
                </form>
            </div>
        </div>

        <!-- Services Grid -->
        @if($layanans->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($layanans as $layanan)
                    <div class="card hover-lift">
                        <div class="relative">
                            @if($layanan->gambar_layanan)
                                <img src="{{ asset('storage/' . $layanan->gambar_layanan) }}" 
                                     alt="{{ $layanan->nama_layanan }}"
                                     class="w-full h-48 object-cover rounded-t-lg">
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-purple-600 rounded-t-lg flex items-center justify-center">
                                    <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            <!-- Category Badge -->
                            <div class="absolute top-2 right-2">
                                <span class="px-3 py-1 text-xs font-medium rounded-full bg-white bg-opacity-90 text-gray-800">
                                    {{ ucfirst($layanan->kategori) }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="p-6">
                            <h4 class="text-xl font-semibold text-gray-900 mb-2">{{ $layanan->nama_layanan }}</h4>
                            <p class="text-gray-600 mb-4 line-clamp-3">{{ $layanan->deskripsi }}</p>
                            
                            <div class="mb-4">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm text-gray-600">Mulai dari</span>
                                    <span class="text-xs text-gray-500">{{ $layanan->durasi_jam }} jam</span>
                                </div>
                                <p class="text-2xl font-bold text-blue-600">{{ $layanan->harga_format }}</p>
                                @if($layanan->harga_premium)
                                    <p class="text-sm text-gray-600">Premium: {{ $layanan->harga_premium_format }}</p>
                                @endif
                            </div>
                            
                            <!-- Features Preview -->
                            @if($layanan->fitur_termasuk && count($layanan->fitur_termasuk) > 0)
                                <div class="mb-4">
                                    <p class="text-sm font-medium text-gray-700 mb-2">Termasuk:</p>
                                    <ul class="text-xs text-gray-600 space-y-1">
                                        @foreach(array_slice($layanan->fitur_termasuk, 0, 3) as $fitur)
                                            <li class="flex items-center">
                                                <svg class="w-3 h-3 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                {{ $fitur }}
                                            </li>
                                        @endforeach
                                        @if(count($layanan->fitur_termasuk) > 3)
                                            <li class="text-blue-600">+{{ count($layanan->fitur_termasuk) - 3 }} fitur lainnya</li>
                                        @endif
                                    </ul>
                                </div>
                            @endif
                            
                            <div class="flex items-center justify-between">
                                <a href="{{ route('user.layanans.show', $layanan) }}" 
                                   class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    Lihat Detail →
                                </a>
                                <a href="{{ route('user.pesanans.create') }}?layanan={{ $layanan->id }}" 
                                   class="btn btn-primary px-4 py-2 text-sm">
                                    Pesan
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="card">
                <div class="p-6">
                    {{ $layanans->links() }}
                </div>
            </div>
        @else
            <!-- Empty State -->
            <div class="card">
                <div class="p-12 text-center">
                    <svg class="w-24 h-24 text-gray-400 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Tidak ada layanan ditemukan</h3>
                    <p class="text-gray-600 mb-6">Coba ubah kata kunci pencarian atau filter kategori.</p>
                    <a href="{{ route('user.layanans.index') }}" class="btn btn-primary px-8 py-3">
                        Lihat Semua Layanan
                    </a>
                </div>
            </div>
        @endif

        <!-- CTA Section -->
        <div class="card bg-gradient-to-r from-blue-500 to-purple-600 text-white">
            <div class="p-8 text-center">
                <h3 class="text-2xl font-bold mb-2">Siap untuk Sesi Foto Profesional?</h3>
                <p class="text-blue-100 mb-6">Hubungi kami untuk konsultasi gratis dan dapatkan penawaran terbaik!</p>
                <div class="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-4">
                    <a href="{{ route('user.pesanans.create') }}" class="btn bg-white text-blue-600 hover:bg-gray-100 px-8 py-3">
                        Pesan Sekarang
                    </a>
                    <a href="https://wa.me/6281234567890" target="_blank" class="btn border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                        </svg>
                        WhatsApp
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-user-sidebar-layout>
