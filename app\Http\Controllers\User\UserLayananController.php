<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Layanan;
use Illuminate\Http\Request;

class UserLayananController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Layanan::aktif();

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_layanan', 'like', "%{$search}%")
                  ->orWhere('kategori', 'like', "%{$search}%")
                  ->orWhere('deskripsi', 'like', "%{$search}%");
            });
        }

        if ($request->has('kategori') && $request->kategori) {
            $query->where('kategori', $request->kategori);
        }

        $layanans = $query->latest()->paginate(12);
        $kategoris = Layanan::aktif()->distinct()->pluck('kategori');

        return view('user.layanans.index', compact('layanans', 'kategoris'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Layanan $layanan)
    {
        return view('user.layanans.show', compact('layanan'));
    }
}
