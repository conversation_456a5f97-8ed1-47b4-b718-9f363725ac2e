<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->string('jenis_acara')->after('ruang_studio');
            $table->integer('jumlah_peserta')->default(1)->after('jenis_acara');
            $table->decimal('durasi', 5, 2)->default(1)->after('jumlah_peserta');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropColumn(['jenis_acara', 'jumlah_peserta', 'durasi']);
        });
    }
};
