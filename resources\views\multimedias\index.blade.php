<x-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('File Multimedia') }}
        </h2>
    </x-slot>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="card hover-lift">
            <div class="card-header p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 gradient-text">File Multimedia</h3>
                        <p class="text-gray-600 mt-1">Kelola file foto, video, dan audio</p>
                    </div>
                    <a href="{{ route('multimedias.create') }}" class="btn btn-primary px-6 py-3">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Upload File
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content Card -->
        <div class="card">
            <div class="p-6">
                <!-- Search Section -->
                <div class="mb-6">
                    <form method="GET" action="{{ route('multimedias.index') }}" class="flex gap-4">
                        <div class="flex-1">
                            <input type="text" name="search" value="{{ request('search') }}" 
                                   placeholder="Cari nama file, kategori, atau nama pelanggan..." 
                                   class="form-input w-full">
                        </div>
                        <button type="submit" class="btn btn-secondary px-6">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Cari
                        </button>
                        @if(request('search'))
                            <a href="{{ route('multimedias.index') }}" class="btn btn-danger px-6">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Reset
                            </a>
                        @endif
                    </form>
                </div>

                <!-- Success Message -->
                @if(session('success'))
                    <div class="alert alert-success slide-in-left">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            {{ session('success') }}
                        </div>
                    </div>
                @endif

                <!-- File Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    @forelse($multimedias as $multimedia)
                        <div class="card hover-lift">
                            <div class="relative">
                                <!-- File Preview -->
                                <div class="h-48 bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-xl flex items-center justify-center">
                                    @if(Str::startsWith($multimedia->tipe_file, 'image/'))
                                        <img src="{{ asset('storage/' . $multimedia->path_file) }}" 
                                             alt="{{ $multimedia->nama_file }}" 
                                             class="w-full h-full object-cover rounded-t-xl">
                                    @elseif(Str::startsWith($multimedia->tipe_file, 'video/'))
                                        <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    @else
                                        <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                                        </svg>
                                    @endif
                                </div>
                                
                                <!-- Category Badge -->
                                <div class="absolute top-3 right-3">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        @if($multimedia->kategori == 'preview') bg-blue-100 text-blue-800
                                        @elseif($multimedia->kategori == 'hasil_edit') bg-green-100 text-green-800
                                        @else bg-yellow-100 text-yellow-800 @endif">
                                        {{ ucfirst($multimedia->kategori) }}
                                    </span>
                                </div>
                            </div>
                            
                            <div class="p-4">
                                <h3 class="text-sm font-semibold text-gray-900 mb-1 truncate">{{ $multimedia->nama_file }}</h3>
                                <p class="text-xs text-gray-600 mb-2">{{ $multimedia->pesanan->pelanggan->nama }}</p>
                                <p class="text-xs text-gray-500 mb-3">{{ number_format($multimedia->ukuran_file / 1024 / 1024, 2) }} MB</p>
                                
                                <div class="flex space-x-1">
                                    <a href="{{ route('multimedias.show', $multimedia) }}" 
                                       class="flex-1 inline-flex items-center justify-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        Lihat
                                    </a>
                                    <a href="{{ route('multimedias.edit', $multimedia) }}" 
                                       class="flex-1 inline-flex items-center justify-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 transition-colors duration-200">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        Edit
                                    </a>
                                    <form action="{{ route('multimedias.destroy', $multimedia) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" 
                                                class="inline-flex items-center justify-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 transition-colors duration-200"
                                                onclick="return confirm('Yakin ingin menghapus file ini?')">
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-span-full">
                            <div class="text-center py-12">
                                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-9 0a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V6a2 2 0 00-2-2m-5 4v6m4-6v6"></path>
                                </svg>
                                <h3 class="text-lg font-semibold text-gray-600 mb-2">Belum ada file multimedia</h3>
                                <p class="text-gray-500 mb-4">Mulai upload file foto, video, atau audio</p>
                                <a href="{{ route('multimedias.create') }}" class="btn btn-primary">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Upload File
                                </a>
                            </div>
                        </div>
                    @endforelse
                </div>

                <!-- Pagination -->
                @if($multimedias->hasPages())
                    <div class="mt-8 flex justify-center">
                        {{ $multimedias->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-sidebar-layout>
