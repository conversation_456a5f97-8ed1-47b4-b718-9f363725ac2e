<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Pemotretan extends Model
{
    use HasFactory;

    protected $fillable = [
        'pesanan_id',
        'tanggal_pemotretan',
        'lokasi',
        'fotografer',
        'jenis_foto',
        'catatan',
    ];

    protected $casts = [
        'tanggal_pemotretan' => 'date',
    ];

    // Relationships
    public function pesanan()
    {
        return $this->belongsTo(Pesanan::class);
    }
}
