<x-user-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Booking Studio Foto') }}
        </h2>
    </x-slot>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-2xl shadow-2xl overflow-hidden">
            <div class="p-8 text-white">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-3xl font-bold mb-2">🏢 Booking Studio Foto</h3>
                        <p class="text-blue-100 text-lg">Pilih studio yang Anda inginkan dan isi data diri</p>
                        <div class="flex items-center space-x-4 mt-4 text-sm text-blue-100">
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Peralatan lengkap</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Lighting profesional</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Booking fleksibel</span>
                            </div>
                        </div>
                    </div>
                    <a href="{{ route('user.bookings.index') }}" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 backdrop-blur-sm">
                        <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Form Card -->
        <div class="card">
            <div class="p-8">
                <form action="{{ route('user.bookings.store') }}" method="POST" class="space-y-8" id="bookingForm">
                    @csrf
                    
                    <!-- Step 1: Pilih Studio -->
                    <div class="space-y-6">
                        <div class="border-b border-gray-200 pb-4">
                            <h4 class="text-lg font-semibold text-gray-900 flex items-center">
                                <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">1</span>
                                Pilih Studio Foto
                            </h4>
                            <p class="text-gray-600 mt-1 ml-11">Pilih jenis studio foto yang Anda inginkan</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($studioRooms as $index => $room)
                                <div class="studio-card group border-2 border-gray-200 rounded-xl p-6 cursor-pointer hover:border-blue-500 hover:shadow-xl transition-all duration-300 bg-white"
                                     data-studio="{{ $room['name'] }}"
                                     data-price="{{ $room['price_per_hour'] }}">

                                    <!-- Studio Image -->
                                    <div class="relative mb-4">
                                        <img src="{{ $room['studio_image'] }}"
                                             alt="{{ $room['name'] }}"
                                             class="w-full h-48 object-cover rounded-lg group-hover:scale-105 transition-transform duration-300"
                                             onerror="this.onerror=null; this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDYwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2MDAiIGhlaWdodD0iNDAwIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIvPgo8cGF0aCBkPSJNMjUwIDIwMEwyNzUgMTc1SDMyNUwyNzUgMjI1SDI1MFoiIGZpbGw9IndoaXRlIiBmaWxsLW9wYWNpdHk9IjAuOCIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzBfMSIgeDE9IjAiIHkxPSIwIiB4Mj0iNjAwIiB5Mj0iNDAwIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIHN0b3AtY29sb3I9IiMzQjgyRjYiLz4KPHN0b3Agb2Zmc2V0PSIwLjUiIHN0b3AtY29sb3I9IiM4QjVDRjYiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjRUM0ODk5Ii8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPC9zdmc+';">
                                    </div>


                                        <!-- Studio Badge -->
                                        <div class="absolute top-3 right-3">
                                            <span class="px-3 py-1 text-xs font-semibold rounded-full bg-white bg-opacity-95 text-gray-800 shadow-lg">
                                                👥 {{ $room['capacity'] }}
                                            </span>
                                        </div>

                                        <!-- Recommended Badge -->
                                        @if($index == 1)
                                            <div class="absolute top-3 left-3">
                                                <span class="px-3 py-1 text-xs font-semibold rounded-full bg-gradient-to-r from-green-400 to-blue-500 text-white shadow-lg">
                                                    ⭐ Rekomendasi
                                                </span>
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Studio Info -->
                                    <div class="space-y-3">
                                        <h5 class="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">{{ $room['name'] }}</h5>
                                        <p class="text-sm text-gray-600 line-clamp-3 leading-relaxed">{{ $room['description'] }}</p>

                                        <!-- Studio Specialties -->
                                        <div class="bg-gray-50 rounded-lg p-3">
                                            <p class="text-xs font-medium text-gray-700 mb-2">Spesialisasi Studio:</p>
                                            <div class="flex flex-wrap gap-1">
                                                @if($room['name'] == 'Studio A - Classic')
                                                    <span class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">Portrait</span>
                                                    <span class="px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">Headshot</span>
                                                    <span class="px-2 py-1 text-xs bg-purple-100 text-purple-700 rounded-full">Produk</span>
                                                @elseif($room['name'] == 'Studio B - Professional')
                                                    <span class="px-2 py-1 text-xs bg-pink-100 text-pink-700 rounded-full">💒 Wedding</span>
                                                    <span class="px-2 py-1 text-xs bg-red-100 text-red-700 rounded-full">👰 Bridal</span>
                                                    <span class="px-2 py-1 text-xs bg-purple-100 text-purple-700 rounded-full">💍 Engagement</span>
                                                    <span class="px-2 py-1 text-xs bg-rose-100 text-rose-700 rounded-full">💐 Pre-Wedding</span>
                                                @else
                                                    <span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full">Fashion</span>
                                                    <span class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">Corporate</span>
                                                    <span class="px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">Commercial</span>
                                                    <span class="px-2 py-1 text-xs bg-purple-100 text-purple-700 rounded-full">Creative</span>
                                                @endif
                                            </div>
                                        </div>

                                        <!-- Pricing -->
                                        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 space-y-2">
                                            <div class="flex items-center justify-between">
                                                <span class="text-sm font-medium text-gray-700">Harga per jam</span>
                                                <span class="text-2xl font-bold text-blue-600">Rp {{ number_format($room['price_per_hour'], 0, ',', '.') }}</span>
                                            </div>
                                            <div class="text-center pt-2 border-t border-gray-200">
                                                <span class="text-xs text-gray-500">👥 Kapasitas: {{ $room['capacity'] }} • ⏱️ Minimum 1 jam</span>
                                            </div>
                                        </div>

                                        <!-- Features Preview -->
                                        <div class="space-y-3">
                                            <p class="text-sm font-medium text-gray-700">Fasilitas Utama:</p>
                                            <div class="grid grid-cols-2 gap-1">
                                                @foreach(array_slice($room['features'], 0, 4) as $feature)
                                                    <div class="flex items-center space-x-1 text-xs text-gray-600">
                                                        <svg class="w-3 h-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                        </svg>
                                                        <span>{{ $feature }}</span>
                                                    </div>
                                                @endforeach
                                            </div>
                                            @if(count($room['features']) > 4)
                                                <p class="text-xs text-blue-600 font-medium">+{{ count($room['features']) - 4 }} fasilitas lainnya</p>
                                            @endif
                                        </div>

                                        <!-- Sample Photos -->
                                        @if(isset($room['sample_photos']) && count($room['sample_photos']) > 0)
                                            <div class="space-y-3">
                                                <div class="flex items-center justify-between">
                                                    <p class="text-sm font-medium text-gray-700">
                                                        @if($room['name'] == 'Studio B - Professional')
                                                            💒 Contoh Foto Wedding:
                                                        @else
                                                            Contoh Hasil Foto:
                                                        @endif
                                                    </p>
                                                    <button type="button" class="text-xs text-blue-600 hover:text-blue-800 font-medium" onclick="showAllPhotos('{{ $room['name'] }}', {{ json_encode($room['sample_photos']) }})">
                                                        Lihat Semua ({{ count($room['sample_photos']) }})
                                                    </button>
                                                </div>
                                                <div class="grid grid-cols-3 gap-2">
                                                    @foreach(array_slice($room['sample_photos'], 0, 3) as $photo)
                                                        <div class="relative group/photo cursor-pointer" onclick="openPhotoModal('{{ $photo }}', 'Contoh foto {{ $room['name'] }}')">
                                                            <img src="{{ $photo }}"
                                                                 alt="Contoh foto {{ $room['name'] }}"
                                                                 class="w-full h-16 object-cover rounded-md group-hover/photo:scale-110 transition-transform duration-300">
                                                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover/photo:bg-opacity-30 transition-all duration-300 rounded-md flex items-center justify-center">
                                                                <svg class="w-4 h-4 text-white opacity-0 group-hover/photo:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                                </svg>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                                <div class="text-center">
                                                    @if($room['name'] == 'Studio B - Professional')
                                                        <p class="text-xs text-gray-500">💒 Spesialis foto wedding dengan hasil yang memukau</p>
                                                        <p class="text-xs text-pink-600 mt-1">Klik foto untuk melihat detail wedding</p>
                                                    @else
                                                        <p class="text-xs text-gray-500">✨ Hasil foto berkualitas tinggi dari studio ini</p>
                                                        <p class="text-xs text-blue-600 mt-1">Klik foto untuk melihat detail</p>
                                                    @endif
                                                </div>
                                            </div>
                                        @endif
                                    </div>

                                    <input type="radio" name="ruang_studio" value="{{ $room['name'] }}" class="hidden studio-radio">
                                </div>
                            @endforeach
                        </div>
                        @error('ruang_studio')
                            <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Step 2: Pilih Waktu -->
                    <div class="space-y-6" id="waktuSection" style="display: none;">
                        <div class="border-b border-gray-200 pb-4">
                            <h4 class="text-lg font-semibold text-gray-900 flex items-center">
                                <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">2</span>
                                Pilih Waktu
                            </h4>
                            <p class="text-gray-600 mt-1 ml-11">Pilih waktu yang sesuai dengan kebutuhan Anda</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Tanggal -->
                            <div class="space-y-2">
                                <label for="tanggal_booking" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Tanggal Diinginkan
                                </label>
                                <input type="date" id="tanggal_booking" name="tanggal_booking" value="{{ old('tanggal_booking') }}" required
                                       min="{{ date('Y-m-d', strtotime('+1 day')) }}"
                                       class="form-input w-full">
                                @error('tanggal_booking')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Jenis Acara -->
                            <div class="space-y-2">
                                <label for="jenis_acara" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                    Jenis Acara
                                </label>
                                <select id="jenis_acara" name="jenis_acara" required class="form-input w-full">
                                    <option value="">Pilih Jenis Acara</option>
                                    <option value="Portrait Session" {{ old('jenis_acara') == 'Portrait Session' ? 'selected' : '' }}>Portrait Session</option>
                                    <option value="Product Photography" {{ old('jenis_acara') == 'Product Photography' ? 'selected' : '' }}>Product Photography</option>
                                    <option value="Family Photo" {{ old('jenis_acara') == 'Family Photo' ? 'selected' : '' }}>Family Photo</option>
                                    <option value="Corporate Headshot" {{ old('jenis_acara') == 'Corporate Headshot' ? 'selected' : '' }}>Corporate Headshot</option>
                                    <option value="Fashion Shoot" {{ old('jenis_acara') == 'Fashion Shoot' ? 'selected' : '' }}>Fashion Shoot</option>
                                    <option value="Content Creation" {{ old('jenis_acara') == 'Content Creation' ? 'selected' : '' }}>Content Creation</option>
                                    <option value="Video Recording" {{ old('jenis_acara') == 'Video Recording' ? 'selected' : '' }}>Video Recording</option>
                                    <option value="Lainnya" {{ old('jenis_acara') == 'Lainnya' ? 'selected' : '' }}>Lainnya</option>
                                </select>
                                @error('jenis_acara')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Jam Mulai -->
                            <div class="space-y-2">
                                <label for="jam_mulai" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Jam Mulai
                                </label>
                                <select id="jam_mulai" name="jam_mulai" required class="form-input w-full">
                                    <option value="">Pilih Jam Mulai</option>
                                    @for($i = 8; $i <= 20; $i++)
                                        <option value="{{ sprintf('%02d:00', $i) }}" {{ old('jam_mulai') == sprintf('%02d:00', $i) ? 'selected' : '' }}>
                                            {{ sprintf('%02d:00', $i) }}
                                        </option>
                                        <option value="{{ sprintf('%02d:30', $i) }}" {{ old('jam_mulai') == sprintf('%02d:30', $i) ? 'selected' : '' }}>
                                            {{ sprintf('%02d:30', $i) }}
                                        </option>
                                    @endfor
                                </select>
                                @error('jam_mulai')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Jam Selesai -->
                            <div class="space-y-2">
                                <label for="jam_selesai" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Jam Selesai
                                </label>
                                <select id="jam_selesai" name="jam_selesai" required class="form-input w-full">
                                    <option value="">Pilih Jam Selesai</option>
                                    @for($i = 9; $i <= 22; $i++)
                                        <option value="{{ sprintf('%02d:00', $i) }}" {{ old('jam_selesai') == sprintf('%02d:00', $i) ? 'selected' : '' }}>
                                            {{ sprintf('%02d:00', $i) }}
                                        </option>
                                        <option value="{{ sprintf('%02d:30', $i) }}" {{ old('jam_selesai') == sprintf('%02d:30', $i) ? 'selected' : '' }}>
                                            {{ sprintf('%02d:30', $i) }}
                                        </option>
                                    @endfor
                                </select>
                                @error('jam_selesai')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Jumlah Peserta -->
                            <div class="space-y-2">
                                <label for="jumlah_peserta" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    Jumlah Peserta
                                </label>
                                <input type="number" id="jumlah_peserta" name="jumlah_peserta" value="{{ old('jumlah_peserta', 1) }}" required min="1" max="20"
                                       placeholder="Berapa orang yang akan ikut sesi foto?"
                                       class="form-input w-full">
                                @error('jumlah_peserta')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Estimasi Biaya -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4" id="estimasiBiaya" style="display: none;">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="font-semibold text-blue-800">Estimasi Biaya</h5>
                                    <p class="text-sm text-blue-600" id="detailBiaya">-</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-2xl font-bold text-blue-600" id="totalBiaya">Rp 0</p>
                                    <p class="text-xs text-blue-600">*Belum termasuk pajak</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Data Diri -->
                    <div class="space-y-6" id="dataDiriSection" style="display: none;">
                        <div class="border-b border-gray-200 pb-4">
                            <h4 class="text-lg font-semibold text-gray-900 flex items-center">
                                <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">3</span>
                                Data Diri
                            </h4>
                            <p class="text-gray-600 mt-1 ml-11">Isi data diri Anda untuk booking studio</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Nama -->
                            <div class="space-y-2">
                                <label for="nama_pelanggan" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    Nama Lengkap
                                </label>
                                <input type="text" id="nama_pelanggan" name="nama_pelanggan" value="{{ old('nama_pelanggan') }}" required
                                       placeholder="Masukkan nama lengkap Anda"
                                       class="form-input w-full">
                                @error('nama_pelanggan')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Email -->
                            <div class="space-y-2">
                                <label for="email_pelanggan" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                    </svg>
                                    Email
                                </label>
                                <input type="email" id="email_pelanggan" name="email_pelanggan" value="{{ old('email_pelanggan') }}" required
                                       placeholder="<EMAIL>"
                                       class="form-input w-full">
                                @error('email_pelanggan')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Kontak -->
                            <div class="space-y-2">
                                <label for="kontak_pelanggan" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                    Nomor WhatsApp
                                </label>
                                <input type="text" id="kontak_pelanggan" name="kontak_pelanggan" value="{{ old('kontak_pelanggan') }}" required
                                       placeholder="08123456789"
                                       class="form-input w-full">
                                @error('kontak_pelanggan')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Alamat -->
                        <div class="space-y-2">
                            <label for="alamat_pelanggan" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Alamat Lengkap
                            </label>
                            <textarea id="alamat_pelanggan" name="alamat_pelanggan" rows="3" required
                                      placeholder="Masukkan alamat lengkap Anda"
                                      class="form-input w-full resize-none">{{ old('alamat_pelanggan') }}</textarea>
                            @error('alamat_pelanggan')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Catatan -->
                        <div class="space-y-2">
                            <label for="catatan" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Catatan Khusus (Opsional)
                            </label>
                            <textarea id="catatan" name="catatan" rows="3"
                                      placeholder="Tambahkan catatan atau permintaan khusus untuk booking studio..."
                                      class="form-input w-full resize-none">{{ old('catatan') }}</textarea>
                            @error('catatan')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200" id="actionButtons" style="display: none;">
                        <a href="{{ route('user.bookings.index') }}"
                           class="btn btn-secondary px-8 py-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Batal
                        </a>
                        <button type="submit" class="btn btn-primary px-8 py-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Booking Sekarang
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Photo Modal -->
    <div id="photoModal" class="fixed inset-0 bg-black bg-opacity-75 z-50 hidden flex items-center justify-center p-4">
        <div class="relative max-w-4xl max-h-full">
            <img id="modalImage" src="" alt="" class="max-w-full max-h-full object-contain rounded-lg">
            <button onclick="closePhotoModal()" class="absolute top-4 right-4 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all duration-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- Gallery Modal -->
    <div id="galleryModal" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden">
        <div class="h-full flex flex-col">
            <!-- Header -->
            <div class="flex items-center justify-between p-6 text-white">
                <h3 id="galleryTitle" class="text-2xl font-bold">Gallery Studio</h3>
                <button onclick="closeGalleryModal()" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all duration-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Gallery Grid -->
            <div class="flex-1 overflow-y-auto p-6">
                <div id="galleryGrid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    <!-- Photos will be inserted here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedStudio = null;
        let studioPrice = 0;

        // Photo modal functions
        function openPhotoModal(imageSrc, altText) {
            const modal = document.getElementById('photoModal');
            const modalImage = document.getElementById('modalImage');
            modalImage.src = imageSrc;
            modalImage.alt = altText;
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closePhotoModal() {
            const modal = document.getElementById('photoModal');
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside
        document.getElementById('photoModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePhotoModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePhotoModal();
                closeGalleryModal();
            }
        });

        // Gallery modal functions
        function showAllPhotos(studioName, photos) {
            const modal = document.getElementById('galleryModal');
            const title = document.getElementById('galleryTitle');
            const grid = document.getElementById('galleryGrid');

            title.textContent = `Gallery ${studioName}`;
            grid.innerHTML = '';

            photos.forEach((photo, index) => {
                const photoDiv = document.createElement('div');
                photoDiv.className = 'relative group cursor-pointer';
                photoDiv.onclick = () => openPhotoModal(photo, `${studioName} - Foto ${index + 1}`);

                photoDiv.innerHTML = `
                    <img src="${photo}"
                         alt="${studioName} - Foto ${index + 1}"
                         class="w-full h-48 object-cover rounded-lg group-hover:scale-105 transition-transform duration-300">
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 rounded-lg flex items-center justify-center">
                        <svg class="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                    </div>
                `;

                grid.appendChild(photoDiv);
            });

            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeGalleryModal() {
            const modal = document.getElementById('galleryModal');
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // Close gallery modal when clicking outside
        document.getElementById('galleryModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeGalleryModal();
            }
        });

        // Studio selection
        document.querySelectorAll('.studio-card').forEach(card => {
            card.addEventListener('click', function() {
                // Remove previous selection
                document.querySelectorAll('.studio-card').forEach(c => {
                    c.classList.remove('border-blue-500', 'bg-blue-50');
                    c.querySelector('.studio-radio').checked = false;
                });

                // Select current
                this.classList.add('border-blue-500', 'bg-blue-50');
                this.querySelector('.studio-radio').checked = true;

                selectedStudio = this.dataset.studio;
                studioPrice = parseInt(this.dataset.price);

                showWaktuSection();
            });
        });



        function showWaktuSection() {
            document.getElementById('waktuSection').style.display = 'block';
            document.getElementById('waktuSection').scrollIntoView({ behavior: 'smooth' });
        }

        function showDataDiriSection() {
            document.getElementById('dataDiriSection').style.display = 'block';
            document.getElementById('dataDiriSection').scrollIntoView({ behavior: 'smooth' });
        }

        function showActionButtons() {
            document.getElementById('actionButtons').style.display = 'flex';
        }

        // Calculate cost when time changes
        function calculateCost() {
            const jamMulai = document.getElementById('jam_mulai').value;
            const jamSelesai = document.getElementById('jam_selesai').value;

            if (jamMulai && jamSelesai && selectedStudio) {
                const startTime = new Date('2000-01-01 ' + jamMulai);
                const endTime = new Date('2000-01-01 ' + jamSelesai);
                const duration = (endTime - startTime) / (1000 * 60 * 60); // hours

                if (duration > 0) {
                    const totalCost = duration * studioPrice;

                    document.getElementById('detailBiaya').textContent =
                        `${selectedStudio} × ${duration} jam × Rp ${studioPrice.toLocaleString('id-ID')}`;
                    document.getElementById('totalBiaya').textContent =
                        'Rp ' + totalCost.toLocaleString('id-ID');
                    document.getElementById('estimasiBiaya').style.display = 'block';

                    // Check if all required fields are filled to show data diri section
                    const requiredFields = ['tanggal_booking', 'jenis_acara', 'jam_mulai', 'jam_selesai', 'jumlah_peserta'];
                    const allFilled = requiredFields.every(field => document.getElementById(field).value.trim() !== '');

                    if (allFilled) {
                        showDataDiriSection();
                    }
                }
            }
        }

        // Add event listeners for time calculation
        document.getElementById('jam_mulai').addEventListener('change', calculateCost);
        document.getElementById('jam_selesai').addEventListener('change', calculateCost);
        document.getElementById('tanggal_booking').addEventListener('change', calculateCost);
        document.getElementById('jenis_acara').addEventListener('change', calculateCost);
        document.getElementById('jumlah_peserta').addEventListener('change', calculateCost);

        // Form validation and show action buttons
        document.getElementById('bookingForm').addEventListener('input', function() {
            // Show action buttons if all required fields are filled
            const requiredFields = ['nama_pelanggan', 'email_pelanggan', 'kontak_pelanggan', 'alamat_pelanggan', 'tanggal_booking', 'jenis_acara', 'jam_mulai', 'jam_selesai', 'jumlah_peserta'];
            const allFilled = requiredFields.every(field => document.getElementById(field).value.trim() !== '');

            if (allFilled && selectedStudio) {
                showActionButtons();
            }
        });
    </script>
</x-user-sidebar-layout>
