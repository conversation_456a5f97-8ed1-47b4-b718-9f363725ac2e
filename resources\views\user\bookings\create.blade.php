<x-user-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Booking Studio Foto') }}
        </h2>
    </x-slot>

    <div class="space-y-6">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-2xl shadow-2xl overflow-hidden">
            <div class="p-8 text-white">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-3xl font-bold mb-2">📸 Booking Studio Foto</h3>
                        <p class="text-blue-100 text-lg">Pilih paket studio yang sesuai kebutuhan Anda</p>
                        <div class="flex items-center space-x-4 mt-4 text-sm text-blue-100">
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Mudah & Cepat</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Harga Transparan</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Konfirmasi Cepat</span>
                            </div>
                        </div>
                    </div>
                    <a href="{{ route('bookings.index') }}" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 backdrop-blur-sm">
                        <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Simple Package Selection -->
        <div class="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <div class="text-center mb-8">
                <h3 class="text-2xl font-bold text-gray-900 mb-2">🎯 Pilih Paket Studio</h3>
                <p class="text-gray-600">Pilih paket yang sesuai dengan kebutuhan foto Anda</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                @foreach($studioPackages as $index => $package)
                    <div class="relative group cursor-pointer" onclick="selectPackage({{ json_encode($package) }}, {{ $index }})">
                        <div class="package-card bg-white rounded-2xl border-3 border-gray-200 hover:border-blue-500 transition-all duration-300 overflow-hidden shadow-lg hover:shadow-2xl transform hover:-translate-y-1" id="package-{{ $index }}">
                            <!-- Package Image -->
                            <div class="relative h-40 overflow-hidden">
                                <img src="{{ $package['image'] }}"
                                     alt="{{ $package['name'] }}"
                                     class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">

                                <!-- Popular Badge -->
                                @if($index == 1)
                                    <div class="absolute top-3 left-3">
                                        <span class="px-3 py-1 text-xs font-bold rounded-full bg-gradient-to-r from-pink-500 to-rose-500 text-white shadow-lg animate-pulse">
                                            ⭐ POPULER
                                        </span>
                                    </div>
                                @endif

                                <!-- Price Badge -->
                                <div class="absolute top-3 right-3">
                                    <span class="px-3 py-1 text-xs font-semibold rounded-full bg-white bg-opacity-95 text-gray-800 shadow-lg">
                                        {{ $package['duration'] }}
                                    </span>
                                </div>

                                <!-- Selected Indicator -->
                                <div class="absolute inset-0 bg-blue-500 bg-opacity-20 hidden items-center justify-center selected-overlay">
                                    <div class="bg-white rounded-full p-3">
                                        <svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <!-- Package Info -->
                            <div class="p-6">
                                <div class="text-center">
                                    <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $package['name'] }}</h3>
                                    <div class="text-3xl font-bold text-blue-600 mb-2">Rp {{ number_format($package['price'], 0, ',', '.') }}</div>
                                    <p class="text-sm text-gray-500 mb-4">{{ $package['duration'] }} • {{ $package['capacity'] }}</p>

                                    <!-- Simple Features -->
                                    <div class="space-y-1 mb-4">
                                        @foreach(array_slice($package['features'], 0, 2) as $feature)
                                            <div class="text-sm text-gray-600">✓ {{ $feature }}</div>
                                        @endforeach
                                        @if(count($package['features']) > 2)
                                            <div class="text-sm text-blue-600">+{{ count($package['features']) - 2 }} lainnya</div>
                                        @endif
                                    </div>

                                    <!-- Selection Indicator -->
                                    <div class="package-selected hidden">
                                        <div class="bg-blue-100 text-blue-800 px-4 py-2 rounded-lg font-medium">
                                            ✅ Paket Dipilih
                                        </div>
                                    </div>

                                    <div class="package-unselected">
                                        <div class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg font-medium hover:bg-blue-100 hover:text-blue-600 transition-colors">
                                            👆 Klik untuk Pilih
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Booking Form (Hidden until package selected) -->
        <div id="bookingFormSection" class="hidden">
            <div class="bg-white rounded-2xl shadow-lg p-8">
                <div class="text-center mb-8">
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">📝 Isi Data Booking</h3>
                    <p class="text-gray-600">Lengkapi informasi untuk melanjutkan booking</p>
                </div>

                <form action="{{ route('bookings.store') }}" method="POST" id="bookingForm" class="space-y-8">
                    @csrf
                    <input type="hidden" id="selectedPackage" name="ruang_studio">
                    <input type="hidden" id="packagePrice" name="package_price">

                    <!-- Selected Package Summary -->
                    <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200" id="packageSummary">
                        <!-- Will be filled by JavaScript -->
                    </div>

                    <!-- Simple Form Steps -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Left Column: Jadwal -->
                        <div class="space-y-6">
                            <div class="bg-blue-50 rounded-xl p-6">
                                <h4 class="font-bold text-lg text-gray-900 mb-4 flex items-center">
                                    <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">1</span>
                                    📅 Pilih Jadwal
                                </h4>

                                <!-- Booking Date -->
                                <div class="mb-4">
                                    <label for="tanggal_booking" class="block text-sm font-medium text-gray-700 mb-2">
                                        Tanggal Foto
                                    </label>
                                    <input type="date" id="tanggal_booking" name="tanggal_booking" required
                                           min="{{ date('Y-m-d', strtotime('+1 day')) }}"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-lg">
                                    @error('tanggal_booking')
                                        <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Time -->
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label for="jam_mulai" class="block text-sm font-medium text-gray-700 mb-2">
                                            Jam Mulai
                                        </label>
                                        <select id="jam_mulai" name="jam_mulai" required
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-lg">
                                            <option value="">Pilih Jam</option>
                                            @for($i = 8; $i <= 20; $i++)
                                                <option value="{{ sprintf('%02d:00', $i) }}">{{ sprintf('%02d:00', $i) }}</option>
                                            @endfor
                                        </select>
                                    </div>
                                    <div>
                                        <label for="jam_selesai" class="block text-sm font-medium text-gray-700 mb-2">
                                            Jam Selesai
                                        </label>
                                        <select id="jam_selesai" name="jam_selesai" required
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-lg">
                                            <option value="">Pilih Jam</option>
                                            @for($i = 9; $i <= 22; $i++)
                                                <option value="{{ sprintf('%02d:00', $i) }}">{{ sprintf('%02d:00', $i) }}</option>
                                            @endfor
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column: Data Diri -->
                        <div class="space-y-6">
                            <div class="bg-purple-50 rounded-xl p-6">
                                <h4 class="font-bold text-lg text-gray-900 mb-4 flex items-center">
                                    <span class="bg-purple-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">2</span>
                                    👤 Data Diri
                                </h4>

                                <!-- Event Type -->
                                <div class="mb-4">
                                    <label for="jenis_acara" class="block text-sm font-medium text-gray-700 mb-2">
                                        Jenis Foto
                                    </label>
                                    <select id="jenis_acara" name="jenis_acara" required
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-lg">
                                        <option value="">Pilih Jenis Foto</option>
                                        <option value="Portrait Session">📸 Portrait</option>
                                        <option value="Wedding Photography">💒 Wedding</option>
                                        <option value="Pre-Wedding">💕 Pre-Wedding</option>
                                        <option value="Family Photo">👨‍👩‍👧‍👦 Keluarga</option>
                                        <option value="Corporate">🏢 Corporate</option>
                                        <option value="Fashion">👗 Fashion</option>
                                        <option value="Product">📦 Produk</option>
                                        <option value="Lainnya">✨ Lainnya</option>
                                    </select>
                                </div>

                                <!-- Participants -->
                                <div>
                                    <label for="jumlah_peserta" class="block text-sm font-medium text-gray-700 mb-2">
                                        Jumlah Orang
                                    </label>
                                    <input type="number" id="jumlah_peserta" name="jumlah_peserta" required min="1" max="20" value="1"
                                           placeholder="Berapa orang yang akan difoto?"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-lg">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="bg-pink-50 rounded-xl p-6">
                        <h4 class="font-bold text-lg text-gray-900 mb-4 flex items-center">
                            <span class="bg-pink-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">3</span>
                            📞 Kontak
                        </h4>

                        <div class="space-y-4">
                            <div>
                                <label for="nama_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                                    Nama Lengkap
                                </label>
                                <input type="text" id="nama_pelanggan" name="nama_pelanggan" required
                                       placeholder="Masukkan nama lengkap Anda"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-lg">
                            </div>

                            <div>
                                <label for="kontak_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                                    WhatsApp
                                </label>
                                <input type="text" id="kontak_pelanggan" name="kontak_pelanggan" required
                                       placeholder="08123456789"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-lg">
                                <p class="text-xs text-gray-500 mt-1">Untuk konfirmasi booking</p>
                            </div>

                            <div>
                                <label for="email_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                                    Email
                                </label>
                                <input type="email" id="email_pelanggan" name="email_pelanggan" required
                                       placeholder="<EMAIL>"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-lg">
                            </div>

                            <div>
                                <label for="alamat_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                                    Alamat
                                </label>
                                <textarea id="alamat_pelanggan" name="alamat_pelanggan" required rows="2"
                                          placeholder="Alamat lengkap Anda"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-colors"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Optional Notes -->
                    <div class="bg-gray-50 rounded-xl p-6">
                        <h4 class="font-bold text-lg text-gray-900 mb-4 flex items-center">
                            <span class="bg-gray-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">4</span>
                            📝 Catatan (Opsional)
                        </h4>

                        <div>
                            <textarea id="catatan" name="catatan" rows="3"
                                      placeholder="Ceritakan konsep foto yang Anda inginkan, tema, atau permintaan khusus lainnya..."
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-colors"></textarea>
                            <p class="text-xs text-gray-500 mt-2">Contoh: "Saya ingin foto pre-wedding dengan tema vintage di studio"</p>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" class="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 text-white font-bold py-4 px-12 rounded-2xl text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:-translate-y-1">
                            ✅ Konfirmasi Booking Sekarang
                        </button>
                        <p class="text-sm text-gray-500 mt-3">Kami akan menghubungi Anda dalam 1x24 jam untuk konfirmasi</p>
                    </div>
                </form>
            </div>
        </div>
    </div>
                    </div>
    <script>
        let selectedPackageIndex = null;

        function selectPackage(package, index) {
            // Reset all packages
            document.querySelectorAll('.package-card').forEach(card => {
                card.classList.remove('border-blue-500', 'bg-blue-50');
                card.classList.add('border-gray-200');
            });

            document.querySelectorAll('.package-selected').forEach(el => el.classList.add('hidden'));
            document.querySelectorAll('.package-unselected').forEach(el => el.classList.remove('hidden'));
            document.querySelectorAll('.selected-overlay').forEach(el => el.classList.add('hidden'));

            // Select current package
            const selectedCard = document.getElementById(`package-${index}`);
            selectedCard.classList.remove('border-gray-200');
            selectedCard.classList.add('border-blue-500', 'bg-blue-50');

            selectedCard.querySelector('.package-selected').classList.remove('hidden');
            selectedCard.querySelector('.package-unselected').classList.add('hidden');
            selectedCard.querySelector('.selected-overlay').classList.remove('hidden');
            selectedCard.querySelector('.selected-overlay').classList.add('flex');

            // Update form
            document.getElementById('selectedPackage').value = package.name;
            document.getElementById('packagePrice').value = package.price;
            selectedPackageIndex = index;

            // Update package summary
            document.getElementById('packageSummary').innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-blue-500 text-white rounded-full w-12 h-12 flex items-center justify-center">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-bold text-xl text-gray-900">📸 ${package.name}</h4>
                            <p class="text-gray-600">${package.description}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-3xl font-bold text-blue-600">Rp ${package.price.toLocaleString('id-ID')}</p>
                        <p class="text-gray-500">${package.duration} • ${package.capacity}</p>
                    </div>
                </div>
            `;

            // Show booking form
            document.getElementById('bookingFormSection').classList.remove('hidden');

            // Smooth scroll to form
            document.getElementById('bookingFormSection').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // Auto-focus on first input
            setTimeout(() => {
                document.getElementById('tanggal_booking').focus();
            }, 500);
        }

        // Form validation and user experience improvements
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-format phone number
            const phoneInput = document.getElementById('kontak_pelanggan');
            if (phoneInput) {
                phoneInput.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.startsWith('0')) {
                        value = '0' + value.slice(1);
                    }
                    e.target.value = value;
                });
            }

            // Form submission with loading state
            const form = document.getElementById('bookingForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    // Check if package is selected
                    if (selectedPackageIndex === null) {
                        e.preventDefault();
                        alert('Silakan pilih paket studio terlebih dahulu!');
                        document.querySelector('.package-card').scrollIntoView({ behavior: 'smooth' });
                        return;
                    }

                    const submitBtn = form.querySelector('button[type="submit"]');
                    submitBtn.innerHTML = '⏳ Memproses Booking...';
                    submitBtn.disabled = true;
                });
            }

            // Auto-calculate end time based on package duration
            const jamMulaiSelect = document.getElementById('jam_mulai');
            const jamSelesaiSelect = document.getElementById('jam_selesai');

            if (jamMulaiSelect && jamSelesaiSelect) {
                jamMulaiSelect.addEventListener('change', function() {
                    if (this.value && selectedPackageIndex !== null) {
                        const startHour = parseInt(this.value.split(':')[0]);
                        const packageDurations = [1, 2, 3]; // hours for each package
                        const duration = packageDurations[selectedPackageIndex];
                        const endHour = startHour + duration;

                        if (endHour <= 22) {
                            jamSelesaiSelect.value = String(endHour).padStart(2, '0') + ':00';
                        }
                    }
                });
            }
        });
    </script>
</x-user-sidebar-layout>
