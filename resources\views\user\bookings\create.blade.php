<x-user-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Booking Studio Foto') }}
        </h2>
    </x-slot>

    <div class="space-y-6">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-2xl shadow-2xl overflow-hidden">
            <div class="p-8 text-white">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-3xl font-bold mb-2">📸 Booking Studio Foto</h3>
                        <p class="text-blue-100 text-lg">Pilih paket studio yang sesuai kebutuhan Anda</p>
                        <div class="flex items-center space-x-4 mt-4 text-sm text-blue-100">
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Mudah & Cepat</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Harga Transparan</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Konfirmasi Cepat</span>
                            </div>
                        </div>
                    </div>
                    <a href="{{ route('bookings.index') }}" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 backdrop-blur-sm">
                        <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Studio Packages -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            @foreach($studioPackages as $index => $package)
                <div class="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 border-2 border-gray-100 hover:border-blue-300">
                    <!-- Package Image -->
                    <div class="relative h-48 overflow-hidden">
                        <img src="{{ $package['image'] }}"
                             alt="{{ $package['name'] }}"
                             class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">

                        <!-- Popular Badge -->
                        @if($index == 1)
                            <div class="absolute top-3 left-3">
                                <span class="px-3 py-1 text-xs font-semibold rounded-full bg-gradient-to-r from-pink-500 to-rose-500 text-white shadow-lg">
                                    ⭐ Populer
                                </span>
                            </div>
                        @endif

                        <!-- Price Badge -->
                        <div class="absolute top-3 right-3">
                            <span class="px-3 py-1 text-xs font-semibold rounded-full bg-white bg-opacity-95 text-gray-800 shadow-lg">
                                {{ $package['duration'] }}
                            </span>
                        </div>
                    </div>

                    <!-- Package Info -->
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">{{ $package['name'] }}</h3>
                            <span class="text-sm text-gray-500">{{ $package['capacity'] }}</span>
                        </div>
                        <p class="text-gray-600 mb-4 leading-relaxed">{{ $package['description'] }}</p>

                        <!-- Price -->
                        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 mb-4">
                            <div class="text-center">
                                <span class="text-3xl font-bold text-blue-600">Rp {{ number_format($package['price'], 0, ',', '.') }}</span>
                                <p class="text-sm text-gray-500 mt-1">{{ $package['duration'] }} • {{ $package['capacity'] }}</p>
                            </div>
                        </div>

                        <!-- Features -->
                        <div class="space-y-2 mb-6">
                            <p class="text-sm font-medium text-gray-700 mb-3">✨ Yang Anda Dapatkan:</p>
                            @foreach($package['features'] as $feature)
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    {{ $feature }}
                                </div>
                            @endforeach
                        </div>

                        <!-- Book Button -->
                        <button type="button"
                                onclick="openBookingModal({{ json_encode($package) }})"
                                class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl">
                            📅 Pilih Paket Ini
                        </button>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Booking Modal -->
        <div id="bookingModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl max-w-lg w-full max-h-[90vh] overflow-y-auto shadow-2xl">
                <form action="{{ route('bookings.store') }}" method="POST" id="bookingForm">
                    @csrf
                    <input type="hidden" id="selectedPackage" name="ruang_studio">
                    <input type="hidden" id="packagePrice" name="package_price">

                    <!-- Modal Header -->
                    <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white rounded-t-2xl">
                        <div class="flex justify-between items-center">
                            <div>
                                <h3 class="text-2xl font-bold" id="modalTitle">📅 Booking Studio</h3>
                                <p class="text-blue-100 mt-1" id="modalDescription">Lengkapi data booking Anda</p>
                            </div>
                            <button type="button" onclick="closeBookingModal()" class="text-white hover:text-gray-200 bg-white bg-opacity-20 hover:bg-opacity-30 p-2 rounded-full transition-all duration-200">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Modal Body -->
                    <div class="p-6 space-y-6">
                        <!-- Package Summary -->
                        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-200" id="packageSummary">
                            <!-- Will be filled by JavaScript -->
                        </div>

                        <!-- Step 1: Jadwal Booking -->
                        <div class="space-y-4">
                            <div class="flex items-center space-x-2 pb-2 border-b border-gray-200">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">1</span>
                                <h4 class="font-semibold text-gray-900">Pilih Jadwal</h4>
                            </div>

                            <!-- Booking Date -->
                            <div>
                                <label for="tanggal_booking" class="block text-sm font-medium text-gray-700 mb-2">
                                    📅 Tanggal Booking
                                </label>
                                <input type="date" id="tanggal_booking" name="tanggal_booking" required
                                       min="{{ date('Y-m-d', strtotime('+1 day')) }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                                @error('tanggal_booking')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                            <!-- Time -->
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label for="jam_mulai" class="block text-sm font-medium text-gray-700 mb-2">
                                        ⏰ Jam Mulai
                                    </label>
                                    <select id="jam_mulai" name="jam_mulai" required
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                                        <option value="">Pilih Jam</option>
                                        @for($i = 8; $i <= 20; $i++)
                                            <option value="{{ sprintf('%02d:00', $i) }}">{{ sprintf('%02d:00', $i) }}</option>
                                            <option value="{{ sprintf('%02d:30', $i) }}">{{ sprintf('%02d:30', $i) }}</option>
                                        @endfor
                                    </select>
                                </div>
                                <div>
                                    <label for="jam_selesai" class="block text-sm font-medium text-gray-700 mb-2">
                                        ⏰ Jam Selesai
                                    </label>
                                    <select id="jam_selesai" name="jam_selesai" required
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                                        <option value="">Pilih Jam</option>
                                        @for($i = 9; $i <= 22; $i++)
                                            <option value="{{ sprintf('%02d:00', $i) }}">{{ sprintf('%02d:00', $i) }}</option>
                                            <option value="{{ sprintf('%02d:30', $i) }}">{{ sprintf('%02d:30', $i) }}</option>
                                        @endfor
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Step 2: Detail Acara -->
                        <div class="space-y-4">
                            <div class="flex items-center space-x-2 pb-2 border-b border-gray-200">
                                <span class="bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">2</span>
                                <h4 class="font-semibold text-gray-900">Detail Acara</h4>
                            </div>

                            <!-- Event Type -->
                            <div>
                                <label for="jenis_acara" class="block text-sm font-medium text-gray-700 mb-2">
                                    🎭 Jenis Acara
                                </label>
                                <select id="jenis_acara" name="jenis_acara" required
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                                    <option value="">Pilih Jenis Acara</option>
                                    <option value="Portrait Session">📸 Portrait Session</option>
                                    <option value="Wedding Photography">💒 Wedding Photography</option>
                                    <option value="Pre-Wedding">💕 Pre-Wedding</option>
                                    <option value="Family Photo">👨‍👩‍👧‍👦 Family Photo</option>
                                    <option value="Corporate">🏢 Corporate</option>
                                    <option value="Fashion">👗 Fashion</option>
                                    <option value="Product">📦 Product</option>
                                    <option value="Lainnya">✨ Lainnya</option>
                                </select>
                            </div>

                            <!-- Participants -->
                            <div>
                                <label for="jumlah_peserta" class="block text-sm font-medium text-gray-700 mb-2">
                                    👥 Jumlah Peserta
                                </label>
                                <input type="number" id="jumlah_peserta" name="jumlah_peserta" required min="1" max="20" value="1"
                                       placeholder="Masukkan jumlah orang yang akan difoto"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                            </div>
                        </div>

                        <!-- Step 3: Data Diri -->
                        <div class="space-y-4">
                            <div class="flex items-center space-x-2 pb-2 border-b border-gray-200">
                                <span class="bg-pink-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">3</span>
                                <h4 class="font-semibold text-gray-900">Data Diri</h4>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="nama_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                                        👤 Nama Lengkap
                                    </label>
                                    <input type="text" id="nama_pelanggan" name="nama_pelanggan" required
                                           placeholder="Masukkan nama lengkap Anda"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                                </div>

                                <div>
                                    <label for="email_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                                        📧 Email
                                    </label>
                                    <input type="email" id="email_pelanggan" name="email_pelanggan" required
                                           placeholder="<EMAIL>"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                                </div>
                            </div>

                            <div>
                                <label for="kontak_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                                    📱 WhatsApp
                                </label>
                                <input type="text" id="kontak_pelanggan" name="kontak_pelanggan" required
                                       placeholder="08123456789"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                                <p class="text-xs text-gray-500 mt-1">Nomor WhatsApp untuk konfirmasi booking</p>
                            </div>

                            <div>
                                <label for="alamat_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                                    🏠 Alamat
                                </label>
                                <textarea id="alamat_pelanggan" name="alamat_pelanggan" required rows="2"
                                          placeholder="Masukkan alamat lengkap Anda"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-colors"></textarea>
                            </div>

                            <div>
                                <label for="catatan" class="block text-sm font-medium text-gray-700 mb-2">
                                    📝 Catatan Tambahan (Opsional)
                                </label>
                                <textarea id="catatan" name="catatan" rows="3"
                                          placeholder="Permintaan khusus, tema foto, atau catatan lainnya..."
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-colors"></textarea>
                                <p class="text-xs text-gray-500 mt-1">Ceritakan konsep atau tema foto yang Anda inginkan</p>
                            </div>
                        </div>
                    </div>

                    <!-- Modal Footer -->
                    <div class="p-6 bg-gray-50 rounded-b-2xl">
                        <div class="flex space-x-3">
                            <button type="button" onclick="closeBookingModal()"
                                    class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-3 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg">
                                ❌ Batal
                            </button>
                            <button type="submit"
                                    class="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg">
                                ✅ Konfirmasi Booking
                            </button>
                        </div>
                        <p class="text-xs text-gray-500 text-center mt-3">Kami akan menghubungi Anda dalam 1x24 jam untuk konfirmasi</p>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function openBookingModal(package) {
            document.getElementById('bookingModal').classList.remove('hidden');
            document.getElementById('modalTitle').textContent = '📅 Booking ' + package.name;
            document.getElementById('modalDescription').textContent = 'Lengkapi data untuk ' + package.name;
            document.getElementById('selectedPackage').value = package.name;
            document.getElementById('packagePrice').value = package.price;

            // Update package summary with better styling
            document.getElementById('packageSummary').innerHTML = `
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-3">
                        <div class="bg-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-bold text-gray-900">${package.name}</h4>
                            <p class="text-sm text-gray-600">${package.description}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-2xl font-bold text-blue-600">Rp ${package.price.toLocaleString('id-ID')}</p>
                        <p class="text-sm text-gray-500">${package.duration} • ${package.capacity}</p>
                    </div>
                </div>
            `;

            document.body.style.overflow = 'hidden';

            // Auto-focus on first input
            setTimeout(() => {
                document.getElementById('tanggal_booking').focus();
            }, 100);
        }

        function closeBookingModal() {
            document.getElementById('bookingModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
            document.getElementById('bookingForm').reset();
        }

        // Close modal when clicking outside
        document.getElementById('bookingModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeBookingModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeBookingModal();
            }
        });

        // Form validation and user experience improvements
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-format phone number
            const phoneInput = document.getElementById('kontak_pelanggan');
            if (phoneInput) {
                phoneInput.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.startsWith('0')) {
                        value = '0' + value.slice(1);
                    }
                    e.target.value = value;
                });
            }

            // Form submission with loading state
            const form = document.getElementById('bookingForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    submitBtn.innerHTML = '⏳ Memproses...';
                    submitBtn.disabled = true;
                });
            }
        });
    </script>
</x-user-sidebar-layout>
