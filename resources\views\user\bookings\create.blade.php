<x-user-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Booking Studio Foto') }}
        </h2>
    </x-slot>

    <div class="space-y-6">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg">
            <div class="p-6 text-white">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold">📸 Booking Studio Foto</h3>
                        <p class="text-blue-100 mt-1">Pilih paket studio yang sesuai kebutuhan Anda</p>
                    </div>
                    <a href="{{ route('user.bookings.index') }}" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all duration-200">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Studio Packages -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            @foreach($studioPackages as $package)
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <!-- Package Image -->
                    <div class="h-48 overflow-hidden">
                        <img src="{{ $package['image'] }}" 
                             alt="{{ $package['name'] }}"
                             class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
                    </div>
                    
                    <!-- Package Info -->
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $package['name'] }}</h3>
                        <p class="text-gray-600 mb-4">{{ $package['description'] }}</p>
                        
                        <!-- Price -->
                        <div class="bg-blue-50 rounded-lg p-4 mb-4">
                            <div class="flex justify-between items-center">
                                <span class="text-2xl font-bold text-blue-600">Rp {{ number_format($package['price'], 0, ',', '.') }}</span>
                                <span class="text-sm text-gray-600">{{ $package['duration'] }}</span>
                            </div>
                            <p class="text-sm text-gray-500 mt-1">{{ $package['capacity'] }}</p>
                        </div>
                        
                        <!-- Features -->
                        <div class="space-y-2 mb-6">
                            @foreach($package['features'] as $feature)
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    {{ $feature }}
                                </div>
                            @endforeach
                        </div>
                        
                        <!-- Book Button -->
                        <button type="button" 
                                onclick="openBookingModal({{ json_encode($package) }})"
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200">
                            📅 Book Sekarang
                        </button>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Booking Modal -->
        <div id="bookingModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
            <div class="bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
                <form action="{{ route('user.bookings.store') }}" method="POST" id="bookingForm">
                    @csrf
                    <input type="hidden" id="selectedPackage" name="ruang_studio">
                    <input type="hidden" id="packagePrice" name="package_price">
                    
                    <!-- Modal Header -->
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-xl font-bold text-gray-900" id="modalTitle">Booking Studio</h3>
                            <button type="button" onclick="closeBookingModal()" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <p class="text-gray-600 mt-1" id="modalDescription">Isi data booking Anda</p>
                    </div>

                    <!-- Modal Body -->
                    <div class="p-6 space-y-4">
                        <!-- Package Summary -->
                        <div class="bg-gray-50 rounded-lg p-4" id="packageSummary">
                            <!-- Will be filled by JavaScript -->
                        </div>
                        
                        <!-- Booking Date -->
                        <div>
                            <label for="tanggal_booking" class="block text-sm font-medium text-gray-700 mb-2">
                                📅 Tanggal Booking
                            </label>
                            <input type="date" id="tanggal_booking" name="tanggal_booking" required
                                   min="{{ date('Y-m-d', strtotime('+1 day')) }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            @error('tanggal_booking')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <!-- Time -->
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="jam_mulai" class="block text-sm font-medium text-gray-700 mb-2">
                                    ⏰ Jam Mulai
                                </label>
                                <select id="jam_mulai" name="jam_mulai" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Pilih Jam</option>
                                    @for($i = 8; $i <= 20; $i++)
                                        <option value="{{ sprintf('%02d:00', $i) }}">{{ sprintf('%02d:00', $i) }}</option>
                                        <option value="{{ sprintf('%02d:30', $i) }}">{{ sprintf('%02d:30', $i) }}</option>
                                    @endfor
                                </select>
                            </div>
                            <div>
                                <label for="jam_selesai" class="block text-sm font-medium text-gray-700 mb-2">
                                    ⏰ Jam Selesai
                                </label>
                                <select id="jam_selesai" name="jam_selesai" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Pilih Jam</option>
                                    @for($i = 9; $i <= 22; $i++)
                                        <option value="{{ sprintf('%02d:00', $i) }}">{{ sprintf('%02d:00', $i) }}</option>
                                        <option value="{{ sprintf('%02d:30', $i) }}">{{ sprintf('%02d:30', $i) }}</option>
                                    @endfor
                                </select>
                            </div>
                        </div>
                        
                        <!-- Event Type -->
                        <div>
                            <label for="jenis_acara" class="block text-sm font-medium text-gray-700 mb-2">
                                🎭 Jenis Acara
                            </label>
                            <select id="jenis_acara" name="jenis_acara" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Pilih Jenis Acara</option>
                                <option value="Portrait Session">Portrait Session</option>
                                <option value="Wedding Photography">Wedding Photography</option>
                                <option value="Pre-Wedding">Pre-Wedding</option>
                                <option value="Family Photo">Family Photo</option>
                                <option value="Corporate">Corporate</option>
                                <option value="Fashion">Fashion</option>
                                <option value="Product">Product</option>
                                <option value="Lainnya">Lainnya</option>
                            </select>
                        </div>
                        
                        <!-- Participants -->
                        <div>
                            <label for="jumlah_peserta" class="block text-sm font-medium text-gray-700 mb-2">
                                👥 Jumlah Peserta
                            </label>
                            <input type="number" id="jumlah_peserta" name="jumlah_peserta" required min="1" max="20" value="1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <!-- Customer Data -->
                        <div class="border-t pt-4">
                            <h4 class="font-medium text-gray-900 mb-4">👤 Data Diri</h4>
                            
                            <div class="space-y-4">
                                <div>
                                    <label for="nama_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                                        Nama Lengkap
                                    </label>
                                    <input type="text" id="nama_pelanggan" name="nama_pelanggan" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                
                                <div>
                                    <label for="email_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                                        Email
                                    </label>
                                    <input type="email" id="email_pelanggan" name="email_pelanggan" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                
                                <div>
                                    <label for="kontak_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                                        WhatsApp
                                    </label>
                                    <input type="text" id="kontak_pelanggan" name="kontak_pelanggan" required
                                           placeholder="08123456789"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                
                                <div>
                                    <label for="alamat_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                                        Alamat
                                    </label>
                                    <textarea id="alamat_pelanggan" name="alamat_pelanggan" required rows="2"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"></textarea>
                                </div>
                                
                                <div>
                                    <label for="catatan" class="block text-sm font-medium text-gray-700 mb-2">
                                        Catatan (Opsional)
                                    </label>
                                    <textarea id="catatan" name="catatan" rows="2"
                                              placeholder="Permintaan khusus atau catatan tambahan..."
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Modal Footer -->
                    <div class="p-6 border-t border-gray-200">
                        <div class="flex space-x-3">
                            <button type="button" onclick="closeBookingModal()"
                                    class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-3 px-4 rounded-lg transition-colors duration-200">
                                Batal
                            </button>
                            <button type="submit"
                                    class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200">
                                📅 Booking Sekarang
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function openBookingModal(package) {
            document.getElementById('bookingModal').classList.remove('hidden');
            document.getElementById('modalTitle').textContent = 'Booking ' + package.name;
            document.getElementById('modalDescription').textContent = package.description;
            document.getElementById('selectedPackage').value = package.name;
            document.getElementById('packagePrice').value = package.price;
            
            // Update package summary
            document.getElementById('packageSummary').innerHTML = `
                <div class="flex justify-between items-center">
                    <div>
                        <h4 class="font-medium text-gray-900">${package.name}</h4>
                        <p class="text-sm text-gray-600">${package.description}</p>
                    </div>
                    <div class="text-right">
                        <p class="text-xl font-bold text-blue-600">Rp ${package.price.toLocaleString('id-ID')}</p>
                        <p class="text-sm text-gray-500">${package.duration} • ${package.capacity}</p>
                    </div>
                </div>
            `;
            
            document.body.style.overflow = 'hidden';
        }

        function closeBookingModal() {
            document.getElementById('bookingModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
            document.getElementById('bookingForm').reset();
        }

        // Close modal when clicking outside
        document.getElementById('bookingModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeBookingModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeBookingModal();
            }
        });
    </script>
</x-user-sidebar-layout>
