<?php

namespace App\Http\Controllers;

use App\Models\Layanan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class LayananController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Layanan::query();

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_layanan', 'like', "%{$search}%")
                  ->orWhere('kategori', 'like', "%{$search}%")
                  ->orWhere('deskripsi', 'like', "%{$search}%");
            });
        }

        $layanans = $query->latest()->paginate(10);

        return view('layanans.index', compact('layanans'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('layanans.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'nama_layanan' => 'required|string|max:255',
            'kategori' => 'required|string|max:255',
            'deskripsi' => 'required|string',
            'harga_dasar' => 'required|numeric|min:0',
            'harga_premium' => 'nullable|numeric|min:0',
            'durasi_jam' => 'required|integer|min:1',
            'fitur_termasuk' => 'required|array',
            'gambar_layanan' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'aktif' => 'boolean'
        ]);

        $data = $request->all();

        if ($request->hasFile('gambar_layanan')) {
            $file = $request->file('gambar_layanan');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $data['gambar_layanan'] = $file->storeAs('layanan', $fileName, 'public');
        }

        Layanan::create($data);

        return redirect()->route('layanans.index')
                        ->with('success', 'Layanan berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Layanan $layanan)
    {
        return view('layanans.show', compact('layanan'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Layanan $layanan)
    {
        return view('layanans.edit', compact('layanan'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Layanan $layanan)
    {
        $request->validate([
            'nama_layanan' => 'required|string|max:255',
            'kategori' => 'required|string|max:255',
            'deskripsi' => 'required|string',
            'harga_dasar' => 'required|numeric|min:0',
            'harga_premium' => 'nullable|numeric|min:0',
            'durasi_jam' => 'required|integer|min:1',
            'fitur_termasuk' => 'required|array',
            'gambar_layanan' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'aktif' => 'boolean'
        ]);

        $data = $request->all();

        if ($request->hasFile('gambar_layanan')) {
            // Delete old image
            if ($layanan->gambar_layanan && Storage::disk('public')->exists($layanan->gambar_layanan)) {
                Storage::disk('public')->delete($layanan->gambar_layanan);
            }

            // Upload new image
            $file = $request->file('gambar_layanan');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $data['gambar_layanan'] = $file->storeAs('layanan', $fileName, 'public');
        }

        $layanan->update($data);

        return redirect()->route('layanans.index')
                        ->with('success', 'Layanan berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Layanan $layanan)
    {
        // Delete image if exists
        if ($layanan->gambar_layanan && Storage::disk('public')->exists($layanan->gambar_layanan)) {
            Storage::disk('public')->delete($layanan->gambar_layanan);
        }

        $layanan->delete();

        return redirect()->route('layanans.index')
                        ->with('success', 'Layanan berhasil dihapus.');
    }
}
