<x-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Dashboard') }}
        </h2>
    </x-slot>

    <div class="space-y-6">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex items-center">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold">Total Pelanggan</h3>
                            <p class="text-3xl font-bold text-blue-600">{{ $totalPelanggan }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex items-center">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold">Total Pesanan</h3>
                            <p class="text-3xl font-bold text-green-600">{{ $totalPesanan }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex items-center">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold">Total Pendapatan</h3>
                            <p class="text-3xl font-bold text-yellow-600">Rp {{ number_format($totalPendapatan, 0, ',', '.') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex items-center">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold">Booking Hari Ini</h3>
                            <p class="text-3xl font-bold text-purple-600">{{ $bookingHariIni }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
                <h3 class="text-lg font-semibold mb-4">Menu Utama</h3>
                <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                    <a href="{{ route('pelanggans.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center">
                        Data Pelanggan
                    </a>
                    <a href="{{ route('pesanans.index') }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center">
                        Data Pesanan
                    </a>
                    <a href="{{ route('pemotretans.index') }}" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded text-center">
                        Riwayat Pemotretan
                    </a>
                    <a href="{{ route('pembayarans.index') }}" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded text-center">
                        Data Pembayaran
                    </a>
                    <a href="{{ route('bookings.index') }}" class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded text-center">
                        Jadwal Booking
                    </a>
                    <a href="{{ route('multimedias.index') }}" class="bg-pink-500 hover:bg-pink-700 text-white font-bold py-2 px-4 rounded text-center">
                        File Multimedia
                    </a>
                    <a href="{{ route('stoks.index') }}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-center">
                        Stok Barang
                    </a>
                    <a href="{{ route('karyawans.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded text-center">
                        Data Karyawan
                    </a>
                    <a href="{{ route('templates.index') }}" class="bg-teal-500 hover:bg-teal-700 text-white font-bold py-2 px-4 rounded text-center">
                        Template & Preset
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-sidebar-layout>
