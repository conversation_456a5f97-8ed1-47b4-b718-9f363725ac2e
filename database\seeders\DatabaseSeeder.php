<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Pelanggan;
use App\Models\Pesanan;
use App\Models\Multimedia;
use App\Models\Stok;
use App\Models\Karyawan;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create default admin user
        User::create([
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin',
        ]);

        // Create default user
        User::create([
            'name' => 'User Test',
            'email' => '<EMAIL>',
            'password' => Hash::make('user123'),
            'role' => 'user',
        ]);

        // Create sample customers
        $pelanggans = [
            [
                'nama' => '<PERSON>',
                'email' => '<EMAIL>',
                'kontak' => '081234567890',
                'alamat' => 'Jl. Contoh No. 123, Jakarta',
            ],
            [
                'nama' => '<PERSON>',
                'email' => '<EMAIL>',
                'kontak' => '081234567891',
                'alamat' => 'Jl. Sample No. 456, Bandung',
            ],
            [
                'nama' => 'Bob Johnson',
                'email' => '<EMAIL>',
                'kontak' => '081234567892',
                'alamat' => 'Jl. Demo No. 789, Surabaya',
            ],
        ];

        foreach ($pelanggans as $pelanggan) {
            Pelanggan::create($pelanggan);
        }

        // Create sample stock items
        $stoks = [
            [
                'nama_barang' => 'Frame Foto 4R',
                'deskripsi' => 'Frame foto ukuran 4R dengan kualitas premium',
                'jumlah_stok' => 50,
                'harga_satuan' => 15000,
                'kategori' => 'Frame',
                'supplier' => 'PT Frame Indonesia',
            ],
            [
                'nama_barang' => 'Album Foto Wedding',
                'deskripsi' => 'Album foto pernikahan dengan cover kulit',
                'jumlah_stok' => 20,
                'harga_satuan' => 250000,
                'kategori' => 'Album',
                'supplier' => 'CV Album Jaya',
            ],
            [
                'nama_barang' => 'Kertas Foto Glossy A4',
                'deskripsi' => 'Kertas foto glossy ukuran A4 untuk cetak foto',
                'jumlah_stok' => 100,
                'harga_satuan' => 5000,
                'kategori' => 'Kertas',
                'supplier' => 'Toko Kertas Makmur',
            ],
        ];

        foreach ($stoks as $stok) {
            Stok::create($stok);
        }

        // Create sample employees
        $karyawans = [
            [
                'nama' => 'Ahmad Fotografer',
                'email' => '<EMAIL>',
                'kontak' => '081234567893',
                'posisi' => 'fotografer',
                'gaji' => 4000000,
                'tanggal_masuk' => '2024-01-15',
                'status' => 'aktif',
                'alamat' => 'Jl. Karyawan No. 1, Jakarta',
            ],
            [
                'nama' => 'Siti Editor',
                'email' => '<EMAIL>',
                'kontak' => '081234567894',
                'posisi' => 'editor',
                'gaji' => 3500000,
                'tanggal_masuk' => '2024-02-01',
                'status' => 'aktif',
                'alamat' => 'Jl. Karyawan No. 2, Jakarta',
            ],
        ];

        foreach ($karyawans as $karyawan) {
            Karyawan::create($karyawan);
        }

        // Seed Multimedias
        Multimedia::create([
            'pesanan_id' => 1,
            'nama_file' => 'wedding_preview_001.jpg',
            'path_file' => 'multimedia/wedding_preview_001.jpg',
            'tipe_file' => 'image/jpeg',
            'ukuran_file' => 2048000, // 2MB
            'kategori' => 'preview',
            'deskripsi' => 'Preview foto wedding couple'
        ]);

        Multimedia::create([
            'pesanan_id' => 1,
            'nama_file' => 'wedding_edited_final.jpg',
            'path_file' => 'multimedia/wedding_edited_final.jpg',
            'tipe_file' => 'image/jpeg',
            'ukuran_file' => 5120000, // 5MB
            'kategori' => 'hasil_edit',
            'deskripsi' => 'Hasil edit final foto wedding'
        ]);

        Multimedia::create([
            'pesanan_id' => 2,
            'nama_file' => 'portrait_raw_001.jpg',
            'path_file' => 'multimedia/portrait_raw_001.jpg',
            'tipe_file' => 'image/jpeg',
            'ukuran_file' => 8192000, // 8MB
            'kategori' => 'raw',
            'deskripsi' => 'File RAW untuk portrait session'
        ]);
    }
}
