<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Stok;
use Illuminate\Http\Request;

class UserStokController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Stok::query();

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_barang', 'like', "%{$search}%")
                  ->orWhere('kategori', 'like', "%{$search}%")
                  ->orWhere('supplier', 'like', "%{$search}%");
            });
        }

        $stoks = $query->latest()->paginate(10);

        return view('user.stoks.index', compact('stoks'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('user.stoks.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'nama_barang' => 'required|string|max:255',
            'deskripsi' => 'nullable|string',
            'jumlah_stok' => 'required|integer|min:0',
            'harga_satuan' => 'required|numeric|min:0',
            'kategori' => 'required|string|max:255',
            'supplier' => 'required|string|max:255',
        ]);

        Stok::create($request->all());

        return redirect()->route('user.stoks.index')
                        ->with('success', 'Stok berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Stok $stok)
    {
        return view('user.stoks.show', compact('stok'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Stok $stok)
    {
        return view('user.stoks.edit', compact('stok'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Stok $stok)
    {
        $request->validate([
            'nama_barang' => 'required|string|max:255',
            'deskripsi' => 'nullable|string',
            'jumlah_stok' => 'required|integer|min:0',
            'harga_satuan' => 'required|numeric|min:0',
            'kategori' => 'required|string|max:255',
            'supplier' => 'required|string|max:255',
        ]);

        $stok->update($request->all());

        return redirect()->route('user.stoks.index')
                        ->with('success', 'Stok berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Stok $stok)
    {
        $stok->delete();

        return redirect()->route('user.stoks.index')
                        ->with('success', 'Stok berhasil dihapus.');
    }
}
