<x-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit File Multimedia') }}
        </h2>
    </x-slot>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="card hover-lift">
            <div class="card-header p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 gradient-text">Edit File Multimedia</h3>
                        <p class="text-gray-600 mt-1">{{ $multimedia->nama_file }}</p>
                    </div>
                    <a href="{{ route('multimedias.show', $multimedia) }}" class="btn btn-secondary px-6 py-3">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Form Card -->
        <div class="card">
            <div class="p-8">
                <form action="{{ route('multimedias.update', $multimedia) }}" method="POST" class="space-y-8">
                    @csrf
                    @method('PUT')
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Current File Preview -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-semibold text-gray-900">File Saat Ini</h4>
                            <div class="bg-gray-100 rounded-xl p-6 text-center">
                                @if(Str::startsWith($multimedia->tipe_file, 'image/'))
                                    <img src="{{ asset('storage/' . $multimedia->path_file) }}" 
                                         alt="{{ $multimedia->nama_file }}" 
                                         class="max-w-full max-h-48 mx-auto rounded-lg shadow-lg">
                                @elseif(Str::startsWith($multimedia->tipe_file, 'video/'))
                                    <video controls class="max-w-full max-h-48 mx-auto rounded-lg shadow-lg">
                                        <source src="{{ asset('storage/' . $multimedia->path_file) }}" type="{{ $multimedia->tipe_file }}">
                                        Browser Anda tidak mendukung video.
                                    </video>
                                @else
                                    <div class="text-center">
                                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <p class="text-sm font-semibold text-gray-600">{{ $multimedia->nama_file }}</p>
                                    </div>
                                @endif
                            </div>
                            <div class="text-center">
                                <p class="text-sm text-gray-600">{{ $multimedia->nama_file }}</p>
                                <p class="text-xs text-gray-500">{{ number_format($multimedia->ukuran_file / 1024 / 1024, 2) }} MB</p>
                            </div>
                        </div>

                        <!-- Form Fields -->
                        <div class="space-y-6">
                            <!-- Pesanan -->
                            <div class="space-y-2">
                                <label for="pesanan_id" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Pesanan
                                </label>
                                <select id="pesanan_id" name="pesanan_id" required class="form-input w-full">
                                    <option value="">Pilih Pesanan</option>
                                    @foreach($pesanans as $pesanan)
                                        <option value="{{ $pesanan->id }}" {{ $multimedia->pesanan_id == $pesanan->id ? 'selected' : '' }}>
                                            #{{ $pesanan->id }} - {{ $pesanan->pelanggan->nama }} ({{ $pesanan->jenis_layanan }})
                                        </option>
                                    @endforeach
                                </select>
                                @error('pesanan_id')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Kategori -->
                            <div class="space-y-2">
                                <label for="kategori" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                    </svg>
                                    Kategori File
                                </label>
                                <select id="kategori" name="kategori" required class="form-input w-full">
                                    <option value="">Pilih Kategori</option>
                                    <option value="preview" {{ $multimedia->kategori == 'preview' ? 'selected' : '' }}>Preview</option>
                                    <option value="hasil_edit" {{ $multimedia->kategori == 'hasil_edit' ? 'selected' : '' }}>Hasil Edit</option>
                                    <option value="raw" {{ $multimedia->kategori == 'raw' ? 'selected' : '' }}>Raw File</option>
                                </select>
                                @error('kategori')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- File Info (Read Only) -->
                            <div class="space-y-4 bg-gray-50 p-4 rounded-lg">
                                <h5 class="font-semibold text-gray-900">Informasi File</h5>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <p class="text-gray-600">Nama File:</p>
                                        <p class="font-medium">{{ $multimedia->nama_file }}</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-600">Tipe File:</p>
                                        <p class="font-medium">{{ $multimedia->tipe_file }}</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-600">Ukuran:</p>
                                        <p class="font-medium">{{ number_format($multimedia->ukuran_file / 1024 / 1024, 2) }} MB</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-600">Upload:</p>
                                        <p class="font-medium">{{ $multimedia->created_at->format('d/m/Y H:i') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Deskripsi -->
                    <div class="space-y-2">
                        <label for="deskripsi" class="form-label">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Deskripsi (Opsional)
                        </label>
                        <textarea id="deskripsi" name="deskripsi" rows="4" 
                                  placeholder="Masukkan deskripsi file multimedia"
                                  class="form-input w-full resize-none">{{ $multimedia->deskripsi }}</textarea>
                        @error('deskripsi')
                            <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Warning Note -->
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-yellow-600 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <div>
                                <h5 class="font-semibold text-yellow-800">Catatan Penting</h5>
                                <p class="text-sm text-yellow-700 mt-1">
                                    Anda hanya dapat mengubah pesanan, kategori, dan deskripsi. File asli tidak dapat diubah. 
                                    Jika perlu mengganti file, silakan hapus dan upload ulang.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                        <a href="{{ route('multimedias.show', $multimedia) }}" 
                           class="btn btn-secondary px-8 py-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Batal
                        </a>
                        <button type="submit" class="btn btn-primary px-8 py-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Update File
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-sidebar-layout>
