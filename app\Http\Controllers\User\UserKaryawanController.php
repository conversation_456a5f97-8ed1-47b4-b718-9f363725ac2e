<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Karyawan;
use Illuminate\Http\Request;

class UserKaryawanController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Karyawan::query();

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('posisi', 'like', "%{$search}%");
            });
        }

        $karyawans = $query->latest()->paginate(10);

        return view('user.karyawans.index', compact('karyawans'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('user.karyawans.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'nama' => 'required|string|max:255',
            'email' => 'required|email|unique:karyawans,email',
            'kontak' => 'required|string|max:255',
            'posisi' => 'required|in:fotografer,editor,admin,resepsionis',
            'gaji' => 'required|numeric|min:0',
            'tanggal_masuk' => 'required|date',
            'status' => 'required|in:aktif,non_aktif',
            'alamat' => 'nullable|string',
        ]);

        Karyawan::create($request->all());

        return redirect()->route('user.karyawans.index')
                        ->with('success', 'Karyawan berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Karyawan $karyawan)
    {
        return view('user.karyawans.show', compact('karyawan'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Karyawan $karyawan)
    {
        return view('user.karyawans.edit', compact('karyawan'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Karyawan $karyawan)
    {
        $request->validate([
            'nama' => 'required|string|max:255',
            'email' => 'required|email|unique:karyawans,email,' . $karyawan->id,
            'kontak' => 'required|string|max:255',
            'posisi' => 'required|in:fotografer,editor,admin,resepsionis',
            'gaji' => 'required|numeric|min:0',
            'tanggal_masuk' => 'required|date',
            'status' => 'required|in:aktif,non_aktif',
            'alamat' => 'nullable|string',
        ]);

        $karyawan->update($request->all());

        return redirect()->route('user.karyawans.index')
                        ->with('success', 'Karyawan berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Karyawan $karyawan)
    {
        $karyawan->delete();

        return redirect()->route('user.karyawans.index')
                        ->with('success', 'Karyawan berhasil dihapus.');
    }
}
