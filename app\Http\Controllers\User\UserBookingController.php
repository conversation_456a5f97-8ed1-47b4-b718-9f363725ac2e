<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\BookingMessage;
use App\Models\Pelanggan;
use Illuminate\Http\Request;

class UserBookingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Booking::with('pelanggan');

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('jenis_acara', 'like', "%{$search}%")
                  ->orWhere('lokasi', 'like', "%{$search}%")
                  ->orWhereHas('pelanggan', function($pelangganQuery) use ($search) {
                      $pelangganQuery->where('nama', 'like', "%{$search}%");
                  });
            });
        }

        $bookings = $query->latest()->paginate(10);

        return view('user.bookings.index', compact('bookings'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Simple studio packages with photos and pricing
        $studioPackages = [
            [
                'id' => 1,
                'name' => 'Studio Classic',
                'description' => 'Paket studio dasar untuk portrait dan foto produk',
                'price' => 150000,
                'duration' => '1 jam',
                'capacity' => '1-5 orang',
                'image' => 'https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=600&h=400&fit=crop&q=80',
                'features' => ['Lighting dasar', 'Background putih', 'Tripod', '10 foto edit']
            ],
            [
                'id' => 2,
                'name' => 'Studio Wedding',
                'description' => 'Paket studio khusus untuk foto wedding dan pre-wedding',
                'price' => 250000,
                'duration' => '2 jam',
                'capacity' => '1-10 orang',
                'image' => 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=400&fit=crop&q=80',
                'features' => ['Professional lighting', 'Multiple backgrounds', 'Props wedding', '20 foto edit']
            ],
            [
                'id' => 3,
                'name' => 'Studio Premium',
                'description' => 'Paket studio premium untuk fashion dan commercial',
                'price' => 400000,
                'duration' => '3 jam',
                'capacity' => '1-20 orang',
                'image' => 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=600&h=400&fit=crop&q=80',
                'features' => ['Advanced lighting', 'Green screen', 'Video equipment', '30 foto edit']
            ]
        ];

        return view('user.bookings.create', compact('studioPackages'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'nama_pelanggan' => 'required|string|max:255',
            'email_pelanggan' => 'required|email|max:255',
            'kontak_pelanggan' => 'required|string|max:20',
            'alamat_pelanggan' => 'required|string',
            'tanggal_booking' => 'required|date|after_or_equal:today',
            'jam_mulai' => 'required',
            'jam_selesai' => 'required|after:jam_mulai',
            'ruang_studio' => 'required|string|max:255',
            'jenis_acara' => 'required|string|max:255',
            'jumlah_peserta' => 'required|integer|min:1',
            'package_price' => 'required|numeric',
            'catatan' => 'nullable|string',
        ]);

        // Create or find pelanggan
        $pelanggan = Pelanggan::firstOrCreate(
            ['email' => $request->email_pelanggan],
            [
                'nama' => $request->nama_pelanggan,
                'kontak' => $request->kontak_pelanggan,
                'alamat' => $request->alamat_pelanggan,
            ]
        );

        // Calculate duration in hours
        $jamMulai = \Carbon\Carbon::parse($request->jam_mulai);
        $jamSelesai = \Carbon\Carbon::parse($request->jam_selesai);
        $durasi = $jamSelesai->diffInHours($jamMulai);

        $data = [
            'pelanggan_id' => $pelanggan->id,
            'tanggal_booking' => $request->tanggal_booking,
            'jam_mulai' => $request->jam_mulai,
            'jam_selesai' => $request->jam_selesai,
            'ruang_studio' => $request->ruang_studio,
            'jenis_acara' => $request->jenis_acara,
            'jumlah_peserta' => $request->jumlah_peserta,
            'durasi' => $durasi,
            'total_harga' => $request->package_price,
            'status' => 'pending',
            'catatan' => $request->catatan,
        ];

        Booking::create($data);

        return redirect()->route('bookings.index')
                        ->with('success', 'Booking studio berhasil dibuat! Kami akan menghubungi Anda segera untuk konfirmasi.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Booking $booking)
    {
        $booking->load('pelanggan');
        return view('user.bookings.show', compact('booking'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Booking $booking)
    {
        $pelanggans = Pelanggan::all();
        return view('user.bookings.edit', compact('booking', 'pelanggans'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Booking $booking)
    {
        $request->validate([
            'pelanggan_id' => 'required|exists:pelanggans,id',
            'tanggal_booking' => 'required|date',
            'waktu_mulai' => 'required',
            'waktu_selesai' => 'required|after:waktu_mulai',
            'durasi' => 'required|numeric|min:1',
            'jenis_acara' => 'required|string|max:255',
            'lokasi' => 'required|string|max:255',
            'status' => 'required|in:pending,confirmed,completed,cancelled',
            'catatan' => 'nullable|string',
        ]);

        $booking->update($request->all());

        return redirect()->route('user.bookings.index')
                        ->with('success', 'Booking berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Booking $booking)
    {
        $booking->delete();

        return redirect()->route('bookings.index')
                        ->with('success', 'Booking berhasil dihapus.');
    }

    /**
     * Send message for booking
     */
    public function sendMessage(Request $request, Booking $booking)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
        ]);

        BookingMessage::create([
            'booking_id' => $booking->id,
            'user_id' => auth()->id(),
            'message' => $request->message,
            'sender_type' => auth()->user()->role === 'admin' ? 'admin' : 'customer',
        ]);

        return redirect()->back()->with('success', 'Pesan berhasil dikirim!');
    }

    /**
     * Get messages for booking
     */
    public function getMessages(Booking $booking)
    {
        $messages = $booking->messages()->with('user')->orderBy('created_at', 'asc')->get();

        // Mark messages as read
        $booking->messages()->where('user_id', '!=', auth()->id())->update(['is_read' => true]);

        return response()->json($messages);
    }
}
