<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Pelanggan;
use Illuminate\Http\Request;

class UserBookingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Booking::with('pelanggan');

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('jenis_acara', 'like', "%{$search}%")
                  ->orWhere('lokasi', 'like', "%{$search}%")
                  ->orWhereHas('pelanggan', function($pelangganQuery) use ($search) {
                      $pelangganQuery->where('nama', 'like', "%{$search}%");
                  });
            });
        }

        $bookings = $query->latest()->paginate(10);

        return view('user.bookings.index', compact('bookings'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Define available studio rooms with pricing and sample photos
        $studioRooms = [
            [
                'name' => 'Studio A - Classic',
                'description' => 'Studio klasik dengan lighting dasar, cocok untuk portrait dan produk',
                'capacity' => '1-5 orang',
                'price_per_hour' => 150000,
                'features' => ['Lighting dasar', 'Background putih', 'Tripod', 'Reflector'],
                'sample_photos' => [
                    'https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?w=400&h=300&fit=crop&q=80', // Portrait session
                    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop&q=80', // Professional headshot
                    'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=300&fit=crop&q=80', // Simple portrait
                    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=300&fit=crop&q=80', // Male portrait
                    'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=300&fit=crop&q=80', // Female portrait
                    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=300&fit=crop&q=80'  // Business portrait
                ],
                'studio_image' => 'https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=600&h=400&fit=crop&q=80'
            ],
            [
                'name' => 'Studio B - Professional',
                'description' => 'Studio profesional dengan peralatan lengkap untuk semua jenis pemotretan wedding',
                'capacity' => '1-10 orang',
                'price_per_hour' => 250000,
                'features' => ['Professional lighting', 'Multiple backgrounds', 'Props', 'Makeup area'],
                'sample_photos' => [
                    'https://images.unsplash.com/photo-1465495976277-4387d4b0e4a6?w=400&h=300&fit=crop&q=80', // Wedding dress detail
                    'https://images.unsplash.com/photo-1511285560929-80b456fea0bc?w=400&h=300&fit=crop&q=80', // Wedding couple kiss
                    'https://images.unsplash.com/photo-1606800052052-a08af7148866?w=400&h=300&fit=crop&q=80', // Beautiful bride portrait
                    'https://images.unsplash.com/photo-1519741497674-611481863552?w=400&h=300&fit=crop&q=80', // Wedding ceremony moment
                    'https://images.unsplash.com/photo-1520854221256-17451cc331bf?w=400&h=300&fit=crop&q=80', // Wedding bouquet
                    'https://images.unsplash.com/photo-1583939003579-730e3918a45a?w=400&h=300&fit=crop&q=80', // Wedding rings exchange
                    'https://images.unsplash.com/photo-1518568814500-bf0f8d125f46?w=400&h=300&fit=crop&q=80', // Romantic couple sunset
                    'https://images.unsplash.com/photo-1469371670807-013ccf25f16a?w=400&h=300&fit=crop&q=80', // Wedding dance
                    'https://images.unsplash.com/photo-1515934751635-c81c6bc9a2d8?w=400&h=300&fit=crop&q=80'  // Bride getting ready
                ],
                'studio_image' => 'https://images.unsplash.com/photo-1606914469633-ca9bf0fa4d1d?w=600&h=400&fit=crop&q=80'
            ],
            [
                'name' => 'Studio C - Premium',
                'description' => 'Studio premium dengan teknologi terbaru dan ruang yang luas',
                'capacity' => '1-20 orang',
                'price_per_hour' => 400000,
                'features' => ['Advanced lighting system', 'Green screen', 'Video equipment', 'Waiting area'],
                'sample_photos' => [
                    'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=400&h=300&fit=crop&q=80', // Fashion model
                    'https://images.unsplash.com/photo-1522673607200-164d1b6ce486?w=400&h=300&fit=crop&q=80', // Corporate team
                    'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&h=300&fit=crop&q=80', // Creative portrait
                    'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop&q=80', // Fashion shoot
                    'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400&h=300&fit=crop&q=80', // Commercial photo
                    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80'  // Group corporate
                ],
                'studio_image' => 'https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=600&h=400&fit=crop&q=80'
            ]
        ];

        return view('user.bookings.create', compact('studioRooms'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'nama_pelanggan' => 'required|string|max:255',
            'email_pelanggan' => 'required|email|max:255',
            'kontak_pelanggan' => 'required|string|max:20',
            'alamat_pelanggan' => 'required|string',
            'tanggal_booking' => 'required|date|after_or_equal:today',
            'jam_mulai' => 'required',
            'jam_selesai' => 'required|after:jam_mulai',
            'ruang_studio' => 'required|string|max:255',
            'jenis_acara' => 'required|string|max:255',
            'jumlah_peserta' => 'required|integer|min:1',
            'catatan' => 'nullable|string',
        ]);

        // Create or find pelanggan
        $pelanggan = Pelanggan::firstOrCreate(
            ['email' => $request->email_pelanggan],
            [
                'nama' => $request->nama_pelanggan,
                'kontak' => $request->kontak_pelanggan,
                'alamat' => $request->alamat_pelanggan,
            ]
        );

        // Calculate duration in hours
        $jamMulai = \Carbon\Carbon::parse($request->jam_mulai);
        $jamSelesai = \Carbon\Carbon::parse($request->jam_selesai);
        $durasi = $jamSelesai->diffInHours($jamMulai);

        $data = [
            'pelanggan_id' => $pelanggan->id,
            'tanggal_booking' => $request->tanggal_booking,
            'jam_mulai' => $request->jam_mulai,
            'jam_selesai' => $request->jam_selesai,
            'ruang_studio' => $request->ruang_studio,
            'jenis_acara' => $request->jenis_acara,
            'jumlah_peserta' => $request->jumlah_peserta,
            'durasi' => $durasi,
            'status' => 'pending',
            'catatan' => $request->catatan,
        ];

        Booking::create($data);

        return redirect()->route('user.bookings.index')
                        ->with('success', 'Booking studio berhasil dibuat! Kami akan menghubungi Anda segera untuk konfirmasi.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Booking $booking)
    {
        $booking->load('pelanggan');
        return view('user.bookings.show', compact('booking'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Booking $booking)
    {
        $pelanggans = Pelanggan::all();
        return view('user.bookings.edit', compact('booking', 'pelanggans'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Booking $booking)
    {
        $request->validate([
            'pelanggan_id' => 'required|exists:pelanggans,id',
            'tanggal_booking' => 'required|date',
            'waktu_mulai' => 'required',
            'waktu_selesai' => 'required|after:waktu_mulai',
            'durasi' => 'required|numeric|min:1',
            'jenis_acara' => 'required|string|max:255',
            'lokasi' => 'required|string|max:255',
            'status' => 'required|in:pending,confirmed,completed,cancelled',
            'catatan' => 'nullable|string',
        ]);

        $booking->update($request->all());

        return redirect()->route('user.bookings.index')
                        ->with('success', 'Booking berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Booking $booking)
    {
        $booking->delete();

        return redirect()->route('user.bookings.index')
                        ->with('success', 'Booking berhasil dihapus.');
    }
}
