@tailwind base;
@tailwind components;
@tailwind utilities;

/* TokoPhotoStudio Custom Styles */

/* CSS Variables for Consistent Theming */
:root {
    --primary-900: #0f172a;
    --primary-800: #1e293b;
    --primary-700: #334155;
    --primary-600: #475569;
    --primary-500: #64748b;
    --blue-600: #2563eb;
    --blue-700: #1d4ed8;
    --blue-500: #3b82f6;
    --blue-400: #60a5fa;
    --blue-300: #93c5fd;
    --blue-200: #bfdbfe;
    --blue-100: #dbeafe;
    --green-600: #16a34a;
    --green-500: #22c55e;
    --yellow-600: #ca8a04;
    --yellow-500: #eab308;
    --red-600: #dc2626;
    --red-500: #ef4444;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --white: #ffffff;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Figtree', sans-serif;
    background-color: var(--gray-50);
    color: var(--gray-900);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* Login Page Styles */
.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-900) 0%, #1e40af 50%, var(--primary-800) 100%);
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(147, 197, 253, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.login-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: var(--shadow-2xl);
    transition: all 0.3s ease;
}

.login-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.4);
}

.login-logo {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 24px;
    transition: all 0.3s ease;
}

.login-logo:hover {
    transform: scale(1.05);
    background: rgba(255, 255, 255, 0.15);
}

.login-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 12px 16px;
    color: var(--white);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.login-input::placeholder {
    color: rgba(191, 219, 254, 0.7);
}

.login-input:focus {
    outline: none;
    border-color: var(--blue-400);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

.login-button {
    background: linear-gradient(135deg, var(--blue-600) 0%, var(--blue-700) 100%);
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    color: var(--white);
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-lg);
}

.login-button:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-xl);
    background: linear-gradient(135deg, var(--blue-700) 0%, #1e3a8a 100%);
}

.login-button:active {
    transform: translateY(0);
}

/* Sidebar Styles */
.sidebar {
    background: linear-gradient(180deg, var(--primary-900) 0%, var(--primary-800) 100%);
    box-shadow: var(--shadow-xl);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.sidebar-nav-item {
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.sidebar-nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.sidebar-nav-item:hover::before {
    left: 100%;
}

.sidebar-nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(4px);
}

.sidebar-nav-item.active {
    background: linear-gradient(135deg, var(--blue-600) 0%, var(--blue-700) 100%);
    box-shadow: var(--shadow-lg);
}

.sidebar-user-section {
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.sidebar-logout-btn {
    background: var(--primary-700);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar-logout-btn:hover {
    background: var(--primary-600);
    transform: translateY(-1px);
}

/* Card Styles */
.card {
    background: var(--white);
    border-radius: 16px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    border-bottom: 1px solid var(--gray-200);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    border-radius: 16px 16px 0 0;
}

/* Button Styles */
.btn {
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--blue-600) 0%, var(--blue-700) 100%);
    color: var(--white);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--blue-700) 0%, #1e3a8a 100%);
    color: var(--white);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--gray-500) 0%, var(--gray-600) 100%);
    color: var(--white);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--gray-600) 0%, var(--gray-700) 100%);
    color: var(--white);
}

.btn-success {
    background: linear-gradient(135deg, var(--green-500) 0%, var(--green-600) 100%);
    color: var(--white);
}

.btn-warning {
    background: linear-gradient(135deg, var(--yellow-500) 0%, var(--yellow-600) 100%);
    color: var(--white);
}

.btn-danger {
    background: linear-gradient(135deg, var(--red-500) 0%, var(--red-600) 100%);
    color: var(--white);
}

/* Form Styles */
.form-input {
    border: 2px solid var(--gray-200);
    border-radius: 10px;
    padding: 12px 16px;
    transition: all 0.3s ease;
    background: var(--white);
    width: 100%;
}

.form-input:focus {
    outline: none;
    border-color: var(--blue-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: #eff6ff;
}

.form-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 8px;
    display: block;
}

/* Table Styles */
.table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.table thead {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
}

.table th {
    font-weight: 700;
    color: var(--gray-700);
    padding: 16px;
    border-bottom: 2px solid var(--gray-200);
    text-align: left;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.05em;
}

.table td {
    padding: 16px;
    border-bottom: 1px solid var(--gray-100);
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background: #eff6ff;
}

/* Badge Styles */
.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
}

.badge-success {
    background: linear-gradient(135deg, var(--green-100) 0%, #dcfce7 100%);
    color: var(--green-800);
}

.badge-warning {
    background: linear-gradient(135deg, var(--yellow-100) 0%, #fef3c7 100%);
    color: var(--yellow-800);
}

.badge-danger {
    background: linear-gradient(135deg, #fecaca 0%, #fee2e2 100%);
    color: var(--red-800);
}

.badge-info {
    background: linear-gradient(135deg, var(--blue-100) 0%, var(--blue-200) 100%);
    color: var(--blue-800);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

/* Utility Classes */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-text {
    background: linear-gradient(135deg, var(--blue-600) 0%, var(--blue-400) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* Alert Styles */
.alert {
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
    border-left: 4px solid;
    animation: slideInLeft 0.5s ease-out;
}

.alert-success {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    border-left-color: var(--green-500);
    color: var(--green-800);
}

.alert-error {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    border-left-color: var(--red-500);
    color: var(--red-800);
}

.alert-warning {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-left-color: var(--yellow-500);
    color: var(--yellow-800);
}

.alert-info {
    background: linear-gradient(135deg, var(--blue-50) 0%, var(--blue-100) 100%);
    border-left-color: var(--blue-500);
    color: var(--blue-800);
}
