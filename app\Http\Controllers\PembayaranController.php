<?php

namespace App\Http\Controllers;

use App\Models\Pembayaran;
use App\Models\Pesanan;
use Illuminate\Http\Request;

class PembayaranController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Pembayaran::with('pesanan.pelanggan');

        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where('metode_pembayaran', 'like', "%{$search}%")
                  ->orWhere('status', 'like', "%{$search}%")
                  ->orWhereHas('pesanan.pelanggan', function($q) use ($search) {
                      $q->where('nama', 'like', "%{$search}%");
                  });
        }

        $pembayarans = $query->orderBy('tanggal_pembayaran', 'desc')->paginate(10);

        return view('pembayarans.index', compact('pembayarans'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $pesanans = Pesanan::with('pelanggan')->get();
        return view('pembayarans.create', compact('pesanans'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'pesanan_id' => 'required|exists:pesanans,id',
            'jumlah_bayar' => 'required|numeric|min:0',
            'metode_pembayaran' => 'required|in:cash,transfer,kartu_kredit,e_wallet',
            'tanggal_pembayaran' => 'required|date',
            'status' => 'required|in:pending,lunas,gagal',
            'invoice_number' => 'required|string|unique:pembayarans,invoice_number',
            'catatan' => 'nullable|string',
        ]);

        Pembayaran::create($request->all());

        return redirect()->route('pembayarans.index')
                        ->with('success', 'Pembayaran berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
