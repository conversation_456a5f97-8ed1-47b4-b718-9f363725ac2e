<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Pelanggan extends Model
{
    use HasFactory;

    protected $fillable = [
        'nama',
        'kontak',
        'email',
        'alamat',
    ];

    // Relationships
    public function pesanans()
    {
        return $this->hasMany(Pesanan::class);
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }
}
