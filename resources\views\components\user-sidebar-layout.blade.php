<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'Laravel') }}</title>
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased bg-gray-50">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="sidebar fixed inset-y-0 left-0 z-50 w-64 flex flex-col">
            <!-- Logo -->
            <div class="sidebar-logo flex items-center justify-center h-20 px-6 flex-shrink-0">
                <div class="flex items-center space-x-4">
                    <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-3 shadow-lg hover-lift">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-white font-bold text-xl gradient-text">TokoPhoto</h1>
                        <p class="text-green-300 text-sm font-medium">User Portal</p>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 overflow-y-auto mt-8 px-4 pb-20">
                <div class="space-y-2">
                    <!-- Dashboard -->
                    <a href="{{ route('dashboard.user') }}" 
                       class="sidebar-nav-item flex items-center px-4 py-4 text-sm font-semibold {{ request()->routeIs('dashboard.user') ? 'active text-white' : 'text-gray-300 hover:text-white' }}">
                        <div class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center mr-4">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                            </svg>
                        </div>
                        <span>Dashboard</span>
                    </a>

                    <!-- Customer Services Section -->
                    <div class="pt-6 pb-3">
                        <div class="flex items-center px-4">
                            <div class="flex-1 h-px bg-gradient-to-r from-transparent via-gray-600 to-transparent"></div>
                            <p class="px-3 text-xs font-bold text-gray-400 uppercase tracking-wider">Layanan Pelanggan</p>
                            <div class="flex-1 h-px bg-gradient-to-r from-transparent via-gray-600 to-transparent"></div>
                        </div>
                    </div>

                    <!-- Layanan Foto -->
                    <a href="{{ route('user.layanans.index') }}"
                       class="sidebar-nav-item flex items-center px-4 py-3 text-sm font-medium {{ request()->routeIs('user.layanans*') ? 'active text-white' : 'text-gray-300 hover:text-white' }}">
                        <div class="w-8 h-8 rounded-lg bg-white/10 flex items-center justify-center mr-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                        <span>Layanan Foto</span>
                    </a>

                    <!-- Pesan Layanan -->
                    <a href="{{ route('user.pesanans.create') }}"
                       class="sidebar-nav-item flex items-center px-4 py-3 text-sm font-medium {{ request()->routeIs('user.pesanans.create') ? 'active text-white' : 'text-gray-300 hover:text-white' }}">
                        <div class="w-8 h-8 rounded-lg bg-white/10 flex items-center justify-center mr-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        <span>Pesan Layanan</span>
                        <span class="ml-auto text-xs bg-green-500 text-white px-2 py-1 rounded-full">Baru</span>
                    </a>

                    <!-- Booking Studio -->
                    <a href="{{ route('user.bookings.create') }}"
                       class="sidebar-nav-item flex items-center px-4 py-3 text-sm font-medium {{ request()->routeIs('user.bookings.create') ? 'active text-white' : 'text-gray-300 hover:text-white' }}">
                        <div class="w-8 h-8 rounded-lg bg-white/10 flex items-center justify-center mr-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <span>Booking Studio</span>
                        <span class="ml-auto text-xs bg-green-500 text-white px-2 py-1 rounded-full">Baru</span>
                    </a>

                    <!-- Data Diri -->
                    <a href="{{ route('user.pelanggans.index') }}"
                       class="sidebar-nav-item flex items-center px-4 py-3 text-sm font-medium {{ request()->routeIs('user.pelanggans*') ? 'active text-white' : 'text-gray-300 hover:text-white' }}">
                        <div class="w-8 h-8 rounded-lg bg-white/10 flex items-center justify-center mr-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <span>Data Diri</span>
                    </a>

                    <!-- Riwayat Section -->
                    <div class="pt-6 pb-3">
                        <div class="flex items-center px-4">
                            <div class="flex-1 h-px bg-gradient-to-r from-transparent via-gray-600 to-transparent"></div>
                            <p class="px-3 text-xs font-bold text-gray-400 uppercase tracking-wider">Riwayat</p>
                            <div class="flex-1 h-px bg-gradient-to-r from-transparent via-gray-600 to-transparent"></div>
                        </div>
                    </div>

                    <!-- Riwayat Pesanan -->
                    <a href="{{ route('user.pesanans.index') }}"
                       class="sidebar-nav-item flex items-center px-4 py-3 text-sm font-medium {{ request()->routeIs('user.pesanans.index') || request()->routeIs('user.pesanans.show') || request()->routeIs('user.pesanans.edit') ? 'active text-white' : 'text-gray-300 hover:text-white' }}">
                        <div class="w-8 h-8 rounded-lg bg-white/10 flex items-center justify-center mr-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <span>Riwayat Pesanan</span>
                    </a>

                    <!-- Riwayat Booking -->
                    <a href="{{ route('user.bookings.index') }}"
                       class="sidebar-nav-item flex items-center px-4 py-3 text-sm font-medium {{ request()->routeIs('user.bookings.index') || request()->routeIs('user.bookings.show') || request()->routeIs('user.bookings.edit') ? 'active text-white' : 'text-gray-300 hover:text-white' }}">
                        <div class="w-8 h-8 rounded-lg bg-white/10 flex items-center justify-center mr-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <span>Riwayat Booking</span>
                    </a>

                    <!-- Galeri Foto -->
                    <a href="{{ route('user.multimedias.index') }}"
                       class="sidebar-nav-item flex items-center px-4 py-3 text-sm font-medium {{ request()->routeIs('user.multimedias*') ? 'active text-white' : 'text-gray-300 hover:text-white' }}">
                        <div class="w-8 h-8 rounded-lg bg-white/10 flex items-center justify-center mr-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <span>Galeri Foto</span>
                    </a>

                    <!-- Restricted Access Section -->
                    <div class="pt-6 pb-3">
                        <div class="flex items-center px-4">
                            <div class="flex-1 h-px bg-gradient-to-r from-transparent via-red-600 to-transparent"></div>
                            <p class="px-3 text-xs font-bold text-red-400 uppercase tracking-wider">Admin Only</p>
                            <div class="flex-1 h-px bg-gradient-to-r from-transparent via-red-600 to-transparent"></div>
                        </div>
                    </div>

                    <!-- Restricted Items -->
                    <div class="space-y-1 opacity-50">
                        <div class="flex items-center px-4 py-3 text-sm font-medium text-gray-500 cursor-not-allowed">
                            <div class="w-8 h-8 rounded-lg bg-gray-600 flex items-center justify-center mr-3">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                            </div>
                            <span>Data Karyawan</span>
                            <span class="ml-auto text-xs bg-red-500 text-white px-2 py-1 rounded-full">Locked</span>
                        </div>
                        <div class="flex items-center px-4 py-3 text-sm font-medium text-gray-500 cursor-not-allowed">
                            <div class="w-8 h-8 rounded-lg bg-gray-600 flex items-center justify-center mr-3">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                            <span>Data Pembayaran</span>
                            <span class="ml-auto text-xs bg-red-500 text-white px-2 py-1 rounded-full">Locked</span>
                        </div>
                        <div class="flex items-center px-4 py-3 text-sm font-medium text-gray-500 cursor-not-allowed">
                            <div class="w-8 h-8 rounded-lg bg-gray-600 flex items-center justify-center mr-3">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            </div>
                            <span>Stok Barang</span>
                            <span class="ml-auto text-xs bg-red-500 text-white px-2 py-1 rounded-full">Locked</span>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- User Info & Logout -->
            <div class="sidebar-user-section flex-shrink-0 p-4">
                <div class="flex items-center space-x-3 mb-3">
                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium">{{ substr(auth()->user()->name, 0, 1) }}</span>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-white truncate">{{ auth()->user()->name }}</p>
                        <p class="text-xs text-green-300 truncate">User Role</p>
                    </div>
                </div>
                <form method="POST" action="{{ route('logout') }}">
                    @csrf
                    <button type="submit" 
                            class="sidebar-logout-btn w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-300 rounded-lg hover:text-white transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                        Logout
                    </button>
                </form>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 ml-64">
            <!-- Top Header -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div>
                            @isset($header)
                                {{ $header }}
                            @endisset
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-600">{{ now()->format('d M Y, H:i') }}</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">USER</span>
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <span class="text-green-600 text-sm font-medium">{{ substr(auth()->user()->name, 0, 1) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="p-6">
                {{ $slot }}
            </main>
        </div>
    </div>
</body>
</html>
