<x-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Upload File Multimedia') }}
        </h2>
    </x-slot>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="card hover-lift">
            <div class="card-header p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 gradient-text">Upload File Multimedia</h3>
                        <p class="text-gray-600 mt-1">Upload foto, video, atau audio untuk pesanan</p>
                    </div>
                    <a href="{{ route('multimedias.index') }}" class="btn btn-secondary px-6 py-3">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Form Card -->
        <div class="card">
            <div class="p-8">
                <form action="{{ route('multimedias.store') }}" method="POST" enctype="multipart/form-data" class="space-y-8">
                    @csrf
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Pesanan -->
                        <div class="space-y-2">
                            <label for="pesanan_id" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Pesanan
                            </label>
                            <select id="pesanan_id" name="pesanan_id" required class="form-input w-full">
                                <option value="">Pilih Pesanan</option>
                                @foreach($pesanans as $pesanan)
                                    <option value="{{ $pesanan->id }}" {{ old('pesanan_id') == $pesanan->id ? 'selected' : '' }}>
                                        #{{ $pesanan->id }} - {{ $pesanan->pelanggan->nama }} ({{ $pesanan->jenis_layanan }})
                                    </option>
                                @endforeach
                            </select>
                            @error('pesanan_id')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Nama File -->
                        <div class="space-y-2">
                            <label for="nama_file" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                                Nama File
                            </label>
                            <input type="text" id="nama_file" name="nama_file" value="{{ old('nama_file') }}" required
                                   placeholder="Masukkan nama file"
                                   class="form-input w-full">
                            @error('nama_file')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Jenis File -->
                        <div class="space-y-2">
                            <label for="jenis_file" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                                Jenis File
                            </label>
                            <select id="jenis_file" name="jenis_file" required class="form-input w-full">
                                <option value="">Pilih Jenis File</option>
                                <option value="foto" {{ old('jenis_file') == 'foto' ? 'selected' : '' }}>Foto</option>
                                <option value="video" {{ old('jenis_file') == 'video' ? 'selected' : '' }}>Video</option>
                                <option value="audio" {{ old('jenis_file') == 'audio' ? 'selected' : '' }}>Audio</option>
                            </select>
                            @error('jenis_file')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Ukuran File -->
                        <div class="space-y-2">
                            <label for="ukuran_file" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                                </svg>
                                Ukuran File (KB)
                            </label>
                            <input type="number" id="ukuran_file" name="ukuran_file" value="{{ old('ukuran_file') }}" required min="1"
                                   placeholder="Masukkan ukuran file dalam KB"
                                   class="form-input w-full">
                            @error('ukuran_file')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- File Upload -->
                    <div class="space-y-2">
                        <label for="path_file" class="form-label">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            Upload File
                        </label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                            <input type="file" id="path_file" name="path_file" required 
                                   accept=".jpg,.jpeg,.png,.gif,.mp4,.avi,.mov,.mp3,.wav"
                                   class="hidden">
                            <label for="path_file" class="cursor-pointer">
                                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <p class="text-lg font-medium text-gray-700 mb-2">Klik untuk upload file</p>
                                <p class="text-sm text-gray-500">Atau drag & drop file di sini</p>
                                <p class="text-xs text-gray-400 mt-2">Maksimal 50MB - JPG, PNG, GIF, MP4, AVI, MOV, MP3, WAV</p>
                            </label>
                        </div>
                        @error('path_file')
                            <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Deskripsi -->
                    <div class="space-y-2">
                        <label for="deskripsi" class="form-label">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
                            </svg>
                            Deskripsi (Opsional)
                        </label>
                        <textarea id="deskripsi" name="deskripsi" rows="4" 
                                  placeholder="Masukkan deskripsi file, keterangan, atau catatan khusus"
                                  class="form-input w-full resize-none">{{ old('deskripsi') }}</textarea>
                        @error('deskripsi')
                            <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                        <a href="{{ route('multimedias.index') }}" 
                           class="btn btn-secondary px-8 py-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Batal
                        </a>
                        <button type="submit" class="btn btn-primary px-8 py-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            Upload File
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // File upload preview
        document.getElementById('path_file').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Auto-fill nama file
                document.getElementById('nama_file').value = file.name.split('.')[0];
                
                // Auto-fill ukuran file
                document.getElementById('ukuran_file').value = Math.round(file.size / 1024);
                
                // Auto-select jenis file
                const fileType = file.type;
                if (fileType.startsWith('image/')) {
                    document.getElementById('jenis_file').value = 'foto';
                } else if (fileType.startsWith('video/')) {
                    document.getElementById('jenis_file').value = 'video';
                } else if (fileType.startsWith('audio/')) {
                    document.getElementById('jenis_file').value = 'audio';
                }
            }
        });
    </script>
</x-sidebar-layout>
