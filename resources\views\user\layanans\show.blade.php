<x-user-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ $layanan->nama_layanan }}
        </h2>
    </x-slot>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="card hover-lift">
            <div class="card-header p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 gradient-text">{{ $layanan->nama_layanan }}</h3>
                        <p class="text-gray-600 mt-1">{{ ucfirst($layanan->kategori) }} Photography</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{{ route('user.pesanans.create') }}?layanan={{ $layanan->id }}" class="btn btn-primary px-6 py-3">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Pesan Sekarang
                        </a>
                        <a href="{{ route('user.layanans.index') }}" class="btn btn-secondary px-6 py-3">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                            Kembali
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Image & Gallery -->
            <div class="card">
                <div class="p-6">
                    @if($layanan->gambar_layanan)
                        <img src="{{ asset('storage/' . $layanan->gambar_layanan) }}" 
                             alt="{{ $layanan->nama_layanan }}"
                             class="w-full h-80 object-cover rounded-lg shadow-lg">
                    @else
                        <div class="w-full h-80 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <svg class="w-24 h-24 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    @endif
                    
                    <!-- Quick Info -->
                    <div class="mt-6 grid grid-cols-2 gap-4">
                        <div class="bg-blue-50 p-4 rounded-lg text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ $layanan->durasi_jam }}</div>
                            <div class="text-sm text-blue-800">Jam Sesi</div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg text-center">
                            <div class="text-2xl font-bold text-green-600">{{ count($layanan->fitur_termasuk) }}</div>
                            <div class="text-sm text-green-800">Fitur Termasuk</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Service Details -->
            <div class="space-y-6">
                <!-- Pricing Card -->
                <div class="card">
                    <div class="card-header p-6">
                        <h4 class="text-lg font-semibold text-gray-900">Paket & Harga</h4>
                    </div>
                    <div class="p-6 space-y-4">
                        <!-- Basic Package -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex justify-between items-start mb-2">
                                <h5 class="font-semibold text-gray-900">Paket Dasar</h5>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">Populer</span>
                            </div>
                            <p class="text-2xl font-bold text-blue-600 mb-2">{{ $layanan->harga_format }}</p>
                            <p class="text-sm text-gray-600">Paket standar dengan fitur lengkap untuk kebutuhan dasar</p>
                        </div>

                        <!-- Premium Package -->
                        @if($layanan->harga_premium)
                            <div class="border border-purple-200 rounded-lg p-4 bg-purple-50">
                                <div class="flex justify-between items-start mb-2">
                                    <h5 class="font-semibold text-gray-900">Paket Premium</h5>
                                    <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium">Terbaik</span>
                                </div>
                                <p class="text-2xl font-bold text-purple-600 mb-2">{{ $layanan->harga_premium_format }}</p>
                                <p class="text-sm text-gray-600">Paket lengkap dengan fitur premium dan layanan ekstra</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Contact Card -->
                <div class="card bg-gradient-to-r from-blue-500 to-purple-600 text-white">
                    <div class="p-6 text-center">
                        <h4 class="text-lg font-semibold mb-2">Butuh Konsultasi?</h4>
                        <p class="text-blue-100 mb-4">Hubungi kami untuk diskusi detail dan penawaran khusus</p>
                        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                            <a href="https://wa.me/6281234567890" target="_blank" 
                               class="btn bg-white text-blue-600 hover:bg-gray-100 px-4 py-2 text-sm">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                                </svg>
                                WhatsApp
                            </a>
                            <a href="tel:+6281234567890" 
                               class="btn border-white text-white hover:bg-white hover:text-blue-600 px-4 py-2 text-sm">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                Telepon
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Description & Features -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Description -->
            <div class="card">
                <div class="card-header p-6">
                    <h4 class="text-lg font-semibold text-gray-900">Deskripsi Layanan</h4>
                </div>
                <div class="p-6">
                    <p class="text-gray-700 leading-relaxed">{{ $layanan->deskripsi }}</p>
                    
                    <div class="mt-6 grid grid-cols-2 gap-4 text-center">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="text-lg font-semibold text-gray-900">{{ $layanan->kategori }}</div>
                            <div class="text-sm text-gray-600">Kategori</div>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="text-lg font-semibold text-gray-900">{{ $layanan->durasi_jam }} Jam</div>
                            <div class="text-sm text-gray-600">Durasi Sesi</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Features -->
            <div class="card">
                <div class="card-header p-6">
                    <h4 class="text-lg font-semibold text-gray-900">Yang Termasuk dalam Layanan</h4>
                </div>
                <div class="p-6">
                    @if($layanan->fitur_termasuk && count($layanan->fitur_termasuk) > 0)
                        <ul class="space-y-3">
                            @foreach($layanan->fitur_termasuk as $fitur)
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-gray-700">{{ $fitur }}</span>
                                </li>
                            @endforeach
                        </ul>
                    @else
                        <p class="text-gray-600">Fitur akan dijelaskan saat konsultasi.</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- CTA Section -->
        <div class="card bg-gradient-to-r from-blue-500 to-purple-600 text-white">
            <div class="p-8 text-center">
                <h3 class="text-2xl font-bold mb-2">Siap Memulai Sesi Foto?</h3>
                <p class="text-blue-100 mb-6">Pesan layanan {{ $layanan->nama_layanan }} sekarang dan dapatkan hasil foto profesional!</p>
                <div class="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-4">
                    <a href="{{ route('user.pesanans.create') }}?layanan={{ $layanan->id }}" 
                       class="btn bg-white text-blue-600 hover:bg-gray-100 px-8 py-3">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Pesan Sekarang
                    </a>
                    <a href="{{ route('user.layanans.index') }}" 
                       class="btn border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3">
                        Lihat Layanan Lain
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-user-sidebar-layout>
