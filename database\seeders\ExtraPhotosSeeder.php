<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Multimedia;
use App\Models\Pesanan;
use Illuminate\Support\Facades\Storage;

class ExtraPhotosSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Pastikan folder multimedia ada
        if (!Storage::disk('public')->exists('multimedia')) {
            Storage::disk('public')->makeDirectory('multimedia');
        }

        // Ambil pesanan yang ada
        $pesanans = Pesanan::all();
        if ($pesanans->count() == 0) {
            $this->command->info('Tidak ada pesanan. Extra photos seeder dilewati.');
            return;
        }

        // 2 foto tambahan yang sangat cantik
        $extraPhotos = [
            [
                'pesanan_id' => $pesanans->first()->id,
                'nama_file' => 'Romantic Couple Sunset',
                'path_file' => 'multimedia/romantic_couple_sunset.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 2304000,
                'kategori' => 'hasil_edit',
                'deskripsi' => 'Foto romantis pasangan saat sunset dengan pencahayaan golden hour yang memukau.',
                'url_source' => 'https://images.unsplash.com/photo-1518568814500-bf0f8d125f46?w=800&h=600&fit=crop'
            ],
            [
                'pesanan_id' => $pesanans->count() > 1 ? $pesanans->skip(1)->first()->id : $pesanans->first()->id,
                'nama_file' => 'Elegant Wedding Dress Detail',
                'path_file' => 'multimedia/wedding_dress_detail.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 1920000,
                'kategori' => 'preview',
                'deskripsi' => 'Detail gaun pengantin yang elegan dengan fokus pada tekstur dan detail yang indah.',
                'url_source' => 'https://images.unsplash.com/photo-1465495976277-4387d4b0e4a6?w=800&h=600&fit=crop'
            ]
        ];

        foreach ($extraPhotos as $data) {
            // Download dan simpan foto dari Unsplash
            if (isset($data['url_source'])) {
                try {
                    $imageContent = file_get_contents($data['url_source']);
                    if ($imageContent !== false) {
                        Storage::disk('public')->put($data['path_file'], $imageContent);
                        $this->command->info("Downloaded: {$data['nama_file']}");
                    }
                } catch (\Exception $e) {
                    $this->command->warn("Failed to download: {$data['nama_file']}");
                }
                unset($data['url_source']);
            }

            Multimedia::create($data);
        }

        $this->command->info('Extra photos seeder completed! Added ' . count($extraPhotos) . ' beautiful photos.');
    }
}
