<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Tambah Layanan Baru') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-medium">Tambah Layanan Baru</h3>
                        <a href="{{ route('layanans.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                            Kembali
                        </a>
                    </div>

                    <form action="{{ route('layanans.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                        @csrf
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- <PERSON>a <PERSON> -->
                            <div>
                                <label for="nama_layanan" class="block text-sm font-medium text-gray-700">Nama Layanan</label>
                                <input type="text" id="nama_layanan" name="nama_layanan" value="{{ old('nama_layanan') }}" required
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('nama_layanan')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Kategori -->
                            <div>
                                <label for="kategori" class="block text-sm font-medium text-gray-700">Kategori</label>
                                <select id="kategori" name="kategori" required
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <option value="">Pilih Kategori</option>
                                    <option value="wedding" {{ old('kategori') == 'wedding' ? 'selected' : '' }}>Wedding</option>
                                    <option value="portrait" {{ old('kategori') == 'portrait' ? 'selected' : '' }}>Portrait</option>
                                    <option value="product" {{ old('kategori') == 'product' ? 'selected' : '' }}>Product</option>
                                    <option value="family" {{ old('kategori') == 'family' ? 'selected' : '' }}>Family</option>
                                    <option value="event" {{ old('kategori') == 'event' ? 'selected' : '' }}>Event</option>
                                    <option value="maternity" {{ old('kategori') == 'maternity' ? 'selected' : '' }}>Maternity</option>
                                    <option value="fashion" {{ old('kategori') == 'fashion' ? 'selected' : '' }}>Fashion</option>
                                    <option value="video" {{ old('kategori') == 'video' ? 'selected' : '' }}>Video</option>
                                </select>
                                @error('kategori')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Harga Dasar -->
                            <div>
                                <label for="harga_dasar" class="block text-sm font-medium text-gray-700">Harga Dasar (Rp)</label>
                                <input type="number" id="harga_dasar" name="harga_dasar" value="{{ old('harga_dasar') }}" required min="0"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('harga_dasar')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Harga Premium -->
                            <div>
                                <label for="harga_premium" class="block text-sm font-medium text-gray-700">Harga Premium (Rp) - Opsional</label>
                                <input type="number" id="harga_premium" name="harga_premium" value="{{ old('harga_premium') }}" min="0"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('harga_premium')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Durasi -->
                            <div>
                                <label for="durasi_jam" class="block text-sm font-medium text-gray-700">Durasi (Jam)</label>
                                <input type="number" id="durasi_jam" name="durasi_jam" value="{{ old('durasi_jam', 1) }}" required min="1"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('durasi_jam')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Status Aktif -->
                            <div class="flex items-center">
                                <input type="checkbox" id="aktif" name="aktif" value="1" {{ old('aktif', true) ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <label for="aktif" class="ml-2 block text-sm text-gray-900">Layanan Aktif</label>
                            </div>
                        </div>

                        <!-- Deskripsi -->
                        <div>
                            <label for="deskripsi" class="block text-sm font-medium text-gray-700">Deskripsi</label>
                            <textarea id="deskripsi" name="deskripsi" rows="4" required
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">{{ old('deskripsi') }}</textarea>
                            @error('deskripsi')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Fitur Termasuk -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Fitur yang Termasuk</label>
                            <div id="fitur-container">
                                <div class="flex items-center space-x-2 mb-2">
                                    <input type="text" name="fitur_termasuk[]" placeholder="Masukkan fitur..."
                                           class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <button type="button" onclick="addFitur()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">+</button>
                                </div>
                            </div>
                            @error('fitur_termasuk')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Gambar Layanan -->
                        <div>
                            <label for="gambar_layanan" class="block text-sm font-medium text-gray-700">Gambar Layanan</label>
                            <input type="file" id="gambar_layanan" name="gambar_layanan" accept="image/*"
                                   class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                            @error('gambar_layanan')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end space-x-4">
                            <a href="{{ route('layanans.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Batal
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Simpan Layanan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function addFitur() {
            const container = document.getElementById('fitur-container');
            const div = document.createElement('div');
            div.className = 'flex items-center space-x-2 mb-2';
            div.innerHTML = `
                <input type="text" name="fitur_termasuk[]" placeholder="Masukkan fitur..."
                       class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                <button type="button" onclick="removeFitur(this)" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">-</button>
            `;
            container.appendChild(div);
        }

        function removeFitur(button) {
            button.parentElement.remove();
        }
    </script>
</x-app-layout>
