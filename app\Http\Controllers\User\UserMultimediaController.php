<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Multimedia;
use App\Models\Pesanan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class UserMultimediaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Multimedia::with('pesanan.pelanggan');

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_file', 'like', "%{$search}%")
                  ->orWhere('kategori', 'like', "%{$search}%")
                  ->orWhereHas('pesanan.pelanggan', function($pelangganQuery) use ($search) {
                      $pelangganQuery->where('nama', 'like', "%{$search}%");
                  });
            });
        }

        $multimedias = $query->latest()->paginate(10);

        return view('user.multimedias.index', compact('multimedias'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $pesanans = Pesanan::with('pelanggan')->get();
        return view('user.multimedias.create', compact('pesanans'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'pesanan_id' => 'required|exists:pesanans,id',
            'nama_file' => 'required|string|max:255',
            'path_file' => 'required|file|mimes:jpg,jpeg,png,gif,mp4,mov,avi|max:51200', // 50MB max
            'kategori' => 'required|in:preview,hasil_edit,raw',
            'deskripsi' => 'nullable|string',
        ]);

        $file = $request->file('path_file');
        $fileName = time() . '_' . $file->getClientOriginalName();
        $filePath = $file->storeAs('multimedia', $fileName, 'public');

        $data = $request->except('path_file');
        $data['path_file'] = $filePath;
        $data['tipe_file'] = $file->getClientMimeType();
        $data['ukuran_file'] = $file->getSize();

        Multimedia::create($data);

        return redirect()->route('user.multimedias.index')
                        ->with('success', 'File multimedia berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Multimedia $multimedia)
    {
        $multimedia->load('pesanan.pelanggan');
        return view('user.multimedias.show', compact('multimedia'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Multimedia $multimedia)
    {
        $pesanans = Pesanan::with('pelanggan')->get();
        return view('user.multimedias.edit', compact('multimedia', 'pesanans'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Multimedia $multimedia)
    {
        $request->validate([
            'pesanan_id' => 'required|exists:pesanans,id',
            'nama_file' => 'required|string|max:255',
            'path_file' => 'nullable|file|mimes:jpg,jpeg,png,gif,mp4,mov,avi|max:51200',
            'kategori' => 'required|in:preview,hasil_edit,raw',
            'deskripsi' => 'nullable|string',
        ]);

        $data = $request->except('path_file');

        if ($request->hasFile('path_file')) {
            // Delete old file
            if ($multimedia->path_file && Storage::disk('public')->exists($multimedia->path_file)) {
                Storage::disk('public')->delete($multimedia->path_file);
            }

            // Upload new file
            $file = $request->file('path_file');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('multimedia', $fileName, 'public');

            $data['path_file'] = $filePath;
            $data['tipe_file'] = $file->getClientMimeType();
            $data['ukuran_file'] = $file->getSize();
        }

        $multimedia->update($data);

        return redirect()->route('user.multimedias.index')
                        ->with('success', 'File multimedia berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Multimedia $multimedia)
    {
        // Delete file from storage
        if ($multimedia->path_file && Storage::disk('public')->exists($multimedia->path_file)) {
            Storage::disk('public')->delete($multimedia->path_file);
        }

        $multimedia->delete();

        return redirect()->route('user.multimedias.index')
                        ->with('success', 'File multimedia berhasil dihapus.');
    }
}
