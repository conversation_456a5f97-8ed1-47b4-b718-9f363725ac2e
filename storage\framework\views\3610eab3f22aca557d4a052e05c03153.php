<?php if (isset($component)) { $__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-sidebar-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('user-sidebar-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Data Pesanan (Read-Only)')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="card hover-lift">
            <div class="card-header p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 gradient-text">Data Pesanan</h3>
                        <p class="text-gray-600 mt-1">Lihat informasi pesanan layanan studio (Read-Only Access)</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">Read-Only Mode</span>
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Card -->
        <div class="card">
            <div class="p-6">
                <!-- Search Section -->
                <div class="mb-6">
                    <form method="GET" action="<?php echo e(route('user.pesanans.index')); ?>" class="flex gap-4">
                        <div class="flex-1">
                            <input type="text" name="search" value="<?php echo e(request('search')); ?>" 
                                   placeholder="Cari jenis layanan atau nama pelanggan..." 
                                   class="form-input w-full">
                        </div>
                        <button type="submit" class="btn btn-secondary px-6">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Cari
                        </button>
                        <?php if(request('search')): ?>
                            <a href="<?php echo e(route('user.pesanans.index')); ?>" class="btn btn-danger px-6">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Reset
                            </a>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- Table -->
                <div class="overflow-x-auto bg-white rounded-xl shadow-sm border border-gray-200">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
                            <tr>
                                <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">ID</th>
                                <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Pelanggan</th>
                                <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Layanan</th>
                                <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Tanggal</th>
                                <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Harga</th>
                                <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Aksi</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php $__empty_1 = true; $__currentLoopData = $pesanans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pesanan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr class="hover:bg-green-50 transition-colors duration-200">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            #<?php echo e($pesanan->id); ?>

                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-4 shadow-lg">
                                                <span class="text-white font-bold text-lg"><?php echo e(substr($pesanan->pelanggan->nama, 0, 1)); ?></span>
                                            </div>
                                            <div>
                                                <div class="text-sm font-semibold text-gray-900"><?php echo e($pesanan->pelanggan->nama); ?></div>
                                                <div class="text-sm text-gray-500"><?php echo e($pesanan->pelanggan->email); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-semibold text-gray-900"><?php echo e($pesanan->jenis_layanan); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo e(Str::limit($pesanan->deskripsi, 30)); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo e($pesanan->tanggal_pesanan->format('d/m/Y')); ?>

                                        <div class="text-xs text-gray-500"><?php echo e($pesanan->tanggal_pesanan->format('H:i')); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-bold text-gray-900">Rp <?php echo e(number_format($pesanan->harga, 0, ',', '.')); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                            <?php if($pesanan->status == 'selesai'): ?> bg-green-100 text-green-800
                                            <?php elseif($pesanan->status == 'proses'): ?> bg-yellow-100 text-yellow-800
                                            <?php elseif($pesanan->status == 'pending'): ?> bg-blue-100 text-blue-800
                                            <?php else: ?> bg-red-100 text-red-800 <?php endif; ?>">
                                            <?php echo e(ucfirst($pesanan->status)); ?>

                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="<?php echo e(route('user.pesanans.show', $pesanan)); ?>" 
                                           class="inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            Lihat Detail
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="px-6 py-12 text-center">
                                        <div class="flex flex-col items-center">
                                            <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <h3 class="text-lg font-semibold text-gray-600 mb-2">Belum ada data pesanan</h3>
                                            <p class="text-gray-500">Data pesanan akan muncul di sini</p>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if($pesanans->hasPages()): ?>
                    <div class="mt-6 flex justify-center">
                        <?php echo e($pesanans->links()); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14)): ?>
<?php $attributes = $__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14; ?>
<?php unset($__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14)): ?>
<?php $component = $__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14; ?>
<?php unset($__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\TokoPhotoStudio\resources\views/user/pesanans/index.blade.php ENDPATH**/ ?>