<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Layanan extends Model
{
    use HasFactory;

    protected $fillable = [
        'nama_layanan',
        'kategori',
        'deskripsi',
        'harga_dasar',
        'harga_premium',
        'durasi_jam',
        'fitur_termasuk',
        'gambar_layanan',
        'aktif'
    ];

    protected $casts = [
        'fitur_termasuk' => 'array',
        'harga_dasar' => 'decimal:2',
        'harga_premium' => 'decimal:2',
        'aktif' => 'boolean'
    ];

    // Relationship dengan pesanan
    public function pesanans()
    {
        return $this->hasMany(Pesanan::class);
    }

    // Scope untuk layanan aktif
    public function scopeAktif($query)
    {
        return $query->where('aktif', true);
    }

    // Accessor untuk format harga
    public function getHargaFormatAttribute()
    {
        return 'Rp ' . number_format($this->harga_dasar, 0, ',', '.');
    }

    public function getHargaPremiumFormatAttribute()
    {
        if ($this->harga_premium) {
            return 'Rp ' . number_format($this->harga_premium, 0, ',', '.');
        }
        return null;
    }
}
