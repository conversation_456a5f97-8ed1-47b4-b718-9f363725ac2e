<x-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Detail Pemotretan') }}
        </h2>
    </x-slot>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="card hover-lift">
            <div class="card-header p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 gradient-text">Detail Pemotretan #{{ $pemotretan->id }}</h3>
                        <p class="text-gray-600 mt-1">Informasi lengkap pemotretan</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{{ route('pemotretans.edit', $pemotretan) }}" class="btn btn-warning px-6 py-3">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit
                        </a>
                        <a href="{{ route('pemotretans.index') }}" class="btn btn-secondary px-6 py-3">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                            Kembali
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detail Cards -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Informasi Utama -->
            <div class="card">
                <div class="card-header p-6">
                    <h4 class="text-lg font-semibold text-gray-900">Informasi Utama</h4>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Pelanggan</p>
                            <p class="font-semibold text-gray-900">{{ $pemotretan->pesanan->pelanggan->nama }}</p>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Pesanan</p>
                            <p class="font-semibold text-gray-900">#{{ $pemotretan->pesanan->id }} - {{ $pemotretan->pesanan->jenis_layanan }}</p>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Fotografer</p>
                            <p class="font-semibold text-gray-900">{{ $pemotretan->karyawan->nama ?? 'Belum ditentukan' }}</p>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Status</p>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                @if($pemotretan->status == 'selesai') bg-green-100 text-green-800
                                @elseif($pemotretan->status == 'berlangsung') bg-yellow-100 text-yellow-800
                                @elseif($pemotretan->status == 'terjadwal') bg-blue-100 text-blue-800
                                @else bg-red-100 text-red-800 @endif">
                                {{ ucfirst($pemotretan->status) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detail Pemotretan -->
            <div class="card">
                <div class="card-header p-6">
                    <h4 class="text-lg font-semibold text-gray-900">Detail Pemotretan</h4>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <p class="text-sm text-gray-600">Tanggal & Waktu</p>
                        <p class="font-semibold text-gray-900">{{ $pemotretan->tanggal_pemotretan->format('d F Y, H:i') }} WIB</p>
                    </div>

                    <div>
                        <p class="text-sm text-gray-600">Lokasi</p>
                        <p class="font-semibold text-gray-900">{{ $pemotretan->lokasi }}</p>
                    </div>

                    <div>
                        <p class="text-sm text-gray-600">Jenis Foto</p>
                        <p class="font-semibold text-gray-900">{{ $pemotretan->jenis_foto }}</p>
                    </div>

                    <div>
                        <p class="text-sm text-gray-600">Durasi</p>
                        <p class="font-semibold text-gray-900">{{ $pemotretan->durasi }} jam</p>
                    </div>

                    @if($pemotretan->catatan)
                        <div>
                            <p class="text-sm text-gray-600">Catatan</p>
                            <p class="font-semibold text-gray-900">{{ $pemotretan->catatan }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="card">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900">Aksi</h4>
                        <p class="text-sm text-gray-600">Kelola pemotretan ini</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{{ route('pemotretans.edit', $pemotretan) }}" class="btn btn-warning">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit Pemotretan
                        </a>
                        <form action="{{ route('pemotretans.destroy', $pemotretan) }}" method="POST" class="inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger" 
                                    onclick="return confirm('Yakin ingin menghapus pemotretan ini?')">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                Hapus Pemotretan
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-sidebar-layout>
