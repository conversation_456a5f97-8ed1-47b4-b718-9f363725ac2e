<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Layanan;
use Illuminate\Support\Facades\Storage;

class LayananSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Pastikan folder layanan ada
        if (!Storage::disk('public')->exists('layanan')) {
            Storage::disk('public')->makeDirectory('layanan');
        }

        $layanans = [
            [
                'nama_layanan' => 'Wedding Photography Premium',
                'kategori' => 'wedding',
                'deskripsi' => 'Paket foto pernikahan lengkap dengan dokumentasi akad, resepsi, dan sesi foto pre-wedding. Termasuk album premium dan video highlight.',
                'harga_dasar' => 8000000,
                'harga_premium' => 15000000,
                'durasi_jam' => 8,
                'fitur_termasuk' => [
                    'Dokumentasi akad nikah',
                    'Dokumentasi resepsi',
                    'Sesi foto pre-wedding',
                    'Album premium 30x30 cm',
                    'Video highlight 3-5 menit',
                    '500+ foto edited',
                    'USB flashdisk custom',
                    'Makeup artist (paket premium)'
                ],
                'gambar_layanan' => 'layanan/wedding_photography.jpg',
                'url_source' => 'https://images.unsplash.com/photo-1519741497674-611481863552?w=800&h=600&fit=crop'
            ],
            [
                'nama_layanan' => 'Portrait Photography Professional',
                'kategori' => 'portrait',
                'deskripsi' => 'Sesi foto portrait profesional untuk keperluan bisnis, LinkedIn, CV, atau personal branding. Studio lighting dan outdoor session.',
                'harga_dasar' => 1500000,
                'harga_premium' => 2500000,
                'durasi_jam' => 2,
                'fitur_termasuk' => [
                    'Studio session 2 jam',
                    'Outdoor session (premium)',
                    '50+ foto edited',
                    'Retouching profesional',
                    'Berbagai pose dan outfit',
                    'Konsultasi styling',
                    'File high resolution',
                    'Cetak foto 8R (5 lembar)'
                ],
                'gambar_layanan' => 'layanan/portrait_photography.jpg',
                'url_source' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop'
            ],
            [
                'nama_layanan' => 'Product Photography Commercial',
                'kategori' => 'product',
                'deskripsi' => 'Foto produk berkualitas tinggi untuk e-commerce, katalog, dan marketing. White background dan lifestyle photography.',
                'harga_dasar' => 500000,
                'harga_premium' => 1200000,
                'durasi_jam' => 3,
                'fitur_termasuk' => [
                    'White background shots',
                    'Lifestyle photography (premium)',
                    'Multiple angles per produk',
                    'Color correction',
                    'Background removal',
                    'File siap upload e-commerce',
                    'Konsultasi marketing visual',
                    'Revisi unlimited'
                ],
                'gambar_layanan' => 'layanan/product_photography.jpg',
                'url_source' => 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=800&h=600&fit=crop'
            ],
            [
                'nama_layanan' => 'Family Photography Outdoor',
                'kategori' => 'family',
                'deskripsi' => 'Sesi foto keluarga di lokasi outdoor dengan suasana natural dan candid. Perfect untuk kenang-kenangan keluarga.',
                'harga_dasar' => 2000000,
                'harga_premium' => 3500000,
                'durasi_jam' => 3,
                'fitur_termasuk' => [
                    'Outdoor session 3 jam',
                    'Multiple locations (premium)',
                    'Candid dan posed shots',
                    '100+ foto edited',
                    'Album keluarga 20x20 cm',
                    'Konsultasi outfit',
                    'Props photography',
                    'File digital lengkap'
                ],
                'gambar_layanan' => 'layanan/family_photography.jpg',
                'url_source' => 'https://images.unsplash.com/photo-1511895426328-dc8714191300?w=800&h=600&fit=crop'
            ],
            [
                'nama_layanan' => 'Event Photography Corporate',
                'kategori' => 'event',
                'deskripsi' => 'Dokumentasi acara korporat, seminar, launching produk, dan gathering perusahaan dengan hasil profesional.',
                'harga_dasar' => 3000000,
                'harga_premium' => 5000000,
                'durasi_jam' => 6,
                'fitur_termasuk' => [
                    'Dokumentasi full event',
                    'Candid dan formal shots',
                    'Group photos',
                    'Detail shots',
                    '200+ foto edited',
                    'Same day preview',
                    'Online gallery',
                    'Video recap (premium)'
                ],
                'gambar_layanan' => 'layanan/event_photography.jpg',
                'url_source' => 'https://images.unsplash.com/photo-1511578314322-379afb476865?w=800&h=600&fit=crop'
            ],
            [
                'nama_layanan' => 'Maternity Photography',
                'kategori' => 'maternity',
                'deskripsi' => 'Sesi foto kehamilan yang indah dan artistic untuk mengabadikan momen spesial menanti kelahiran si kecil.',
                'harga_dasar' => 1800000,
                'harga_premium' => 2800000,
                'durasi_jam' => 2,
                'fitur_termasuk' => [
                    'Studio dan outdoor session',
                    'Artistic poses',
                    'Props maternity',
                    '60+ foto edited',
                    'Soft retouching',
                    'Album maternity',
                    'Konsultasi outfit',
                    'Partner photos included'
                ],
                'gambar_layanan' => 'layanan/maternity_photography.jpg',
                'url_source' => 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=800&h=600&fit=crop'
            ],
            [
                'nama_layanan' => 'Fashion Photography Editorial',
                'kategori' => 'fashion',
                'deskripsi' => 'Foto fashion editorial untuk model, designer, atau brand fashion. Studio lighting dan creative concept.',
                'harga_dasar' => 2500000,
                'harga_premium' => 4000000,
                'durasi_jam' => 4,
                'fitur_termasuk' => [
                    'Studio session 4 jam',
                    'Creative lighting setup',
                    'Multiple outfits',
                    'Professional retouching',
                    '80+ foto edited',
                    'Makeup artist included',
                    'Styling consultation',
                    'Portfolio ready images'
                ],
                'gambar_layanan' => 'layanan/fashion_photography.jpg',
                'url_source' => 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=800&h=600&fit=crop'
            ],
            [
                'nama_layanan' => 'Video Production Cinematic',
                'kategori' => 'video',
                'deskripsi' => 'Produksi video cinematic untuk wedding, corporate, atau personal project dengan kualitas broadcast.',
                'harga_dasar' => 5000000,
                'harga_premium' => 10000000,
                'durasi_jam' => 8,
                'fitur_termasuk' => [
                    'Pre-production planning',
                    'Multi-camera setup',
                    'Professional audio',
                    'Color grading',
                    'Motion graphics',
                    'Music licensing',
                    'Multiple video formats',
                    'Drone footage (premium)'
                ],
                'gambar_layanan' => 'layanan/video_production.jpg',
                'url_source' => 'https://images.unsplash.com/photo-1492691527719-9d1e07e534b4?w=800&h=600&fit=crop'
            ]
        ];

        foreach ($layanans as $data) {
            // Download dan simpan gambar dari Unsplash
            if (isset($data['url_source'])) {
                try {
                    $imageContent = file_get_contents($data['url_source']);
                    if ($imageContent !== false) {
                        Storage::disk('public')->put($data['gambar_layanan'], $imageContent);
                        $this->command->info("Downloaded: {$data['nama_layanan']}");
                    }
                } catch (\Exception $e) {
                    $this->command->warn("Failed to download: {$data['nama_layanan']}");
                }
                unset($data['url_source']);
            }

            Layanan::create($data);
        }

        $this->command->info('Layanan seeder completed! Added ' . count($layanans) . ' services.');
    }
}
