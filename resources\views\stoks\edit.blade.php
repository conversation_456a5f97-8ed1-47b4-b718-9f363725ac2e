<x-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit Stok Barang') }}
        </h2>
    </x-slot>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="card hover-lift">
            <div class="card-header p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 gradient-text">Edit Stok Barang</h3>
                        <p class="text-gray-600 mt-1">{{ $stok->nama_barang }}</p>
                    </div>
                    <a href="{{ route('stoks.show', $stok) }}" class="btn btn-secondary px-6 py-3">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Form Card -->
        <div class="card">
            <div class="p-8">
                <form action="{{ route('stoks.update', $stok) }}" method="POST" class="space-y-8">
                    @csrf
                    @method('PUT')
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Nama Barang -->
                        <div class="space-y-2">
                            <label for="nama_barang" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                Nama Barang
                            </label>
                            <input type="text" id="nama_barang" name="nama_barang" value="{{ $stok->nama_barang }}" required
                                   placeholder="Masukkan nama barang"
                                   class="form-input w-full">
                            @error('nama_barang')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Kategori -->
                        <div class="space-y-2">
                            <label for="kategori" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                                Kategori
                            </label>
                            <select id="kategori" name="kategori" required class="form-input w-full">
                                <option value="">Pilih Kategori</option>
                                <option value="kamera" {{ $stok->kategori == 'kamera' ? 'selected' : '' }}>Kamera</option>
                                <option value="lensa" {{ $stok->kategori == 'lensa' ? 'selected' : '' }}>Lensa</option>
                                <option value="lighting" {{ $stok->kategori == 'lighting' ? 'selected' : '' }}>Lighting</option>
                                <option value="tripod" {{ $stok->kategori == 'tripod' ? 'selected' : '' }}>Tripod</option>
                                <option value="aksesoris" {{ $stok->kategori == 'aksesoris' ? 'selected' : '' }}>Aksesoris</option>
                                <option value="lainnya" {{ $stok->kategori == 'lainnya' ? 'selected' : '' }}>Lainnya</option>
                            </select>
                            @error('kategori')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Jumlah -->
                        <div class="space-y-2">
                            <label for="jumlah" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
                                </svg>
                                Jumlah
                            </label>
                            <input type="number" id="jumlah" name="jumlah" value="{{ $stok->jumlah }}" required min="0"
                                   placeholder="Masukkan jumlah stok"
                                   class="form-input w-full">
                            @error('jumlah')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Satuan -->
                        <div class="space-y-2">
                            <label for="satuan" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                                Satuan
                            </label>
                            <input type="text" id="satuan" name="satuan" value="{{ $stok->satuan }}" required
                                   placeholder="Contoh: pcs, unit, set"
                                   class="form-input w-full">
                            @error('satuan')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Harga -->
                        <div class="space-y-2">
                            <label for="harga" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                                Harga Satuan (Rp)
                            </label>
                            <input type="number" id="harga" name="harga" value="{{ $stok->harga }}" required min="0" step="1000"
                                   placeholder="Masukkan harga per satuan"
                                   class="form-input w-full">
                            @error('harga')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Status -->
                        <div class="space-y-2">
                            <label for="status" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Status
                            </label>
                            <select id="status" name="status" required class="form-input w-full">
                                <option value="">Pilih Status</option>
                                <option value="tersedia" {{ $stok->status == 'tersedia' ? 'selected' : '' }}>Tersedia</option>
                                <option value="habis" {{ $stok->status == 'habis' ? 'selected' : '' }}>Habis</option>
                                <option value="rusak" {{ $stok->status == 'rusak' ? 'selected' : '' }}>Rusak</option>
                            </select>
                            @error('status')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Deskripsi -->
                    <div class="space-y-2">
                        <label for="deskripsi" class="form-label">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Deskripsi (Opsional)
                        </label>
                        <textarea id="deskripsi" name="deskripsi" rows="4" 
                                  placeholder="Masukkan deskripsi barang, spesifikasi, atau catatan lainnya"
                                  class="form-input w-full resize-none">{{ $stok->deskripsi }}</textarea>
                        @error('deskripsi')
                            <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Info Card -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-blue-600 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <h5 class="font-semibold text-blue-800">Tips Pengelolaan Stok</h5>
                                <ul class="text-sm text-blue-700 mt-1 list-disc list-inside space-y-1">
                                    <li>Pastikan jumlah stok sesuai dengan kondisi fisik barang</li>
                                    <li>Update status menjadi "Habis" jika stok sudah kosong</li>
                                    <li>Gunakan status "Rusak" untuk barang yang perlu diperbaiki</li>
                                    <li>Harga yang diinput akan digunakan untuk perhitungan nilai aset</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                        <a href="{{ route('stoks.show', $stok) }}" 
                           class="btn btn-secondary px-8 py-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Batal
                        </a>
                        <button type="submit" class="btn btn-primary px-8 py-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Update Stok
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Auto update status based on jumlah
        document.getElementById('jumlah').addEventListener('input', function() {
            const jumlah = parseInt(this.value);
            const statusSelect = document.getElementById('status');
            
            if (jumlah === 0) {
                statusSelect.value = 'habis';
            } else if (jumlah > 0 && statusSelect.value === 'habis') {
                statusSelect.value = 'tersedia';
            }
        });
    </script>
</x-sidebar-layout>
