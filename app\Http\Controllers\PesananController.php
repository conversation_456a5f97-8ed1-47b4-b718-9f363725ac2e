<?php

namespace App\Http\Controllers;

use App\Models\Pesanan;
use App\Models\Pelanggan;
use Illuminate\Http\Request;

class PesananController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Pesanan::with('pelanggan');

        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where('jenis_layanan', 'like', "%{$search}%")
                  ->orWhere('paket', 'like', "%{$search}%")
                  ->orWhereHas('pelanggan', function($q) use ($search) {
                      $q->where('nama', 'like', "%{$search}%");
                  });
        }

        $pesanans = $query->paginate(10);

        return view('pesanans.index', compact('pesanans'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $pelanggans = Pelanggan::all();
        return view('pesanans.create', compact('pelanggans'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'pelanggan_id' => 'required|exists:pelanggans,id',
            'jenis_layanan' => 'required|string|max:255',
            'paket' => 'required|string|max:255',
            'harga' => 'required|numeric|min:0',
            'tanggal_pesanan' => 'required|date',
            'status' => 'required|in:pending,proses,selesai,dibatalkan',
            'catatan' => 'nullable|string',
        ]);

        Pesanan::create($request->all());

        return redirect()->route('pesanans.index')
                        ->with('success', 'Pesanan berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
