<?php

namespace App\Http\Controllers;

use App\Models\Pesanan;
use App\Models\Pelanggan;
use App\Models\Layanan;
use Illuminate\Http\Request;

class PesananController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Pesanan::with('pelanggan');

        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where('jenis_layanan', 'like', "%{$search}%")
                  ->orWhere('paket', 'like', "%{$search}%")
                  ->orWhereHas('pelanggan', function($q) use ($search) {
                      $q->where('nama', 'like', "%{$search}%");
                  });
        }

        $pesanans = $query->paginate(10);

        return view('pesanans.index', compact('pesanans'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $pelanggans = Pelanggan::all();
        $layanans = Layanan::aktif()->get();
        return view('pesanans.create', compact('pelanggans', 'layanans'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'pelanggan_id' => 'required|exists:pelanggans,id',
            'layanan_id' => 'required|exists:layanans,id',
            'jenis_layanan' => 'required|string|max:255',
            'paket' => 'required|string|max:255',
            'harga' => 'required|numeric|min:0',
            'tanggal_pesanan' => 'required|date',
            'status' => 'required|in:pending,proses,selesai,dibatalkan',
            'catatan' => 'nullable|string',
        ]);

        Pesanan::create($request->all());

        return redirect()->route('pesanans.index')
                        ->with('success', 'Pesanan berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Pesanan $pesanan)
    {
        $pesanan->load('pelanggan');
        return view('pesanans.show', compact('pesanan'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Pesanan $pesanan)
    {
        $pelanggans = Pelanggan::all();
        $layanans = Layanan::aktif()->get();
        return view('pesanans.edit', compact('pesanan', 'pelanggans', 'layanans'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Pesanan $pesanan)
    {
        $request->validate([
            'pelanggan_id' => 'required|exists:pelanggans,id',
            'layanan_id' => 'required|exists:layanans,id',
            'jenis_layanan' => 'required|string|max:255',
            'paket' => 'required|string|max:255',
            'harga' => 'required|numeric|min:0',
            'tanggal_pesanan' => 'required|date',
            'status' => 'required|in:pending,proses,selesai,dibatalkan',
            'catatan' => 'nullable|string',
        ]);

        $pesanan->update($request->all());

        return redirect()->route('pesanans.index')
                        ->with('success', 'Pesanan berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Pesanan $pesanan)
    {
        $pesanan->delete();

        return redirect()->route('pesanans.index')
                        ->with('success', 'Pesanan berhasil dihapus.');
    }
}
