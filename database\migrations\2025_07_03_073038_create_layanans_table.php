<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('layanans', function (Blueprint $table) {
            $table->id();
            $table->string('nama_layanan');
            $table->string('kategori'); // wedding, portrait, product, video, etc
            $table->text('deskripsi');
            $table->decimal('harga_dasar', 12, 2);
            $table->decimal('harga_premium', 12, 2)->nullable();
            $table->integer('durasi_jam')->default(1);
            $table->json('fitur_termasuk'); // array fitur yang termasuk
            $table->string('gambar_layanan')->nullable();
            $table->boolean('aktif')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('layanans');
    }
};
