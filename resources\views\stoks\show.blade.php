<x-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Detail Stok Barang') }}
        </h2>
    </x-slot>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="card hover-lift">
            <div class="card-header p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 gradient-text">Detail Stok Barang</h3>
                        <p class="text-gray-600 mt-1">{{ $stok->nama_barang }}</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{{ route('stoks.edit', $stok) }}" class="btn btn-warning px-6 py-3">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit
                        </a>
                        <a href="{{ route('stoks.index') }}" class="btn btn-secondary px-6 py-3">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                            Kembali
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detail Cards -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Informasi Barang -->
            <div class="card">
                <div class="card-header p-6">
                    <h4 class="text-lg font-semibold text-gray-900">Informasi Barang</h4>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Nama Barang</p>
                            <p class="font-semibold text-gray-900 text-lg">{{ $stok->nama_barang }}</p>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Kategori</p>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                @if($stok->kategori == 'kamera') bg-blue-100 text-blue-800
                                @elseif($stok->kategori == 'lensa') bg-green-100 text-green-800
                                @elseif($stok->kategori == 'lighting') bg-yellow-100 text-yellow-800
                                @elseif($stok->kategori == 'tripod') bg-purple-100 text-purple-800
                                @else bg-gray-100 text-gray-800 @endif">
                                {{ ucfirst($stok->kategori) }}
                            </span>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Jumlah Stok</p>
                            <p class="font-bold text-2xl text-gray-900">{{ $stok->jumlah }}</p>
                            <p class="text-sm text-gray-500">{{ $stok->satuan }}</p>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Harga Satuan</p>
                            <p class="font-semibold text-gray-900">Rp {{ number_format($stok->harga, 0, ',', '.') }}</p>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Status</p>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                @if($stok->status == 'tersedia') bg-green-100 text-green-800
                                @elseif($stok->status == 'habis') bg-red-100 text-red-800
                                @else bg-yellow-100 text-yellow-800 @endif">
                                {{ ucfirst($stok->status) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detail Tambahan -->
            <div class="card">
                <div class="card-header p-6">
                    <h4 class="text-lg font-semibold text-gray-900">Detail Tambahan</h4>
                </div>
                <div class="p-6 space-y-4">
                    @if($stok->deskripsi)
                        <div>
                            <p class="text-sm text-gray-600 mb-2">Deskripsi</p>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <p class="text-gray-900">{{ $stok->deskripsi }}</p>
                            </div>
                        </div>
                    @endif

                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h5 class="font-semibold text-blue-800 mb-3">Informasi Stok</h5>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-blue-700">Total Nilai Stok:</span>
                                <span class="font-semibold text-blue-900">Rp {{ number_format($stok->jumlah * $stok->harga, 0, ',', '.') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-blue-700">Kategori:</span>
                                <span class="font-semibold text-blue-900">{{ ucfirst($stok->kategori) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-blue-700">Satuan:</span>
                                <span class="font-semibold text-blue-900">{{ $stok->satuan }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-green-50 p-4 rounded-lg">
                            <p class="text-sm text-green-600 mb-1">Tanggal Dibuat</p>
                            <p class="font-semibold text-green-900">{{ $stok->created_at->format('d/m/Y H:i') }}</p>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <p class="text-sm text-yellow-600 mb-1">Terakhir Update</p>
                            <p class="font-semibold text-yellow-900">{{ $stok->updated_at->format('d/m/Y H:i') }}</p>
                        </div>
                    </div>

                    <!-- Status Alert -->
                    @if($stok->jumlah <= 5)
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex">
                                <svg class="w-5 h-5 text-red-600 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                <div>
                                    <h5 class="font-semibold text-red-800">Stok Menipis!</h5>
                                    <p class="text-sm text-red-700 mt-1">
                                        Stok barang ini sudah menipis. Pertimbangkan untuk melakukan restocking.
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="card">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900">Aksi</h4>
                        <p class="text-sm text-gray-600">Kelola stok barang ini</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{{ route('stoks.edit', $stok) }}" class="btn btn-warning">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit Stok
                        </a>
                        <form action="{{ route('stoks.destroy', $stok) }}" method="POST" class="inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger" 
                                    onclick="return confirm('Yakin ingin menghapus stok ini?')">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                Hapus Stok
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-sidebar-layout>
