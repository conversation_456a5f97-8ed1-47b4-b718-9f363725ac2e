<?php

namespace App\Http\Controllers;

use App\Models\Multimedia;
use App\Models\Pesanan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class MultimediaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Multimedia::with('pesanan.pelanggan');

        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where('nama_file', 'like', "%{$search}%")
                  ->orWhere('kategori', 'like', "%{$search}%")
                  ->orWhereHas('pesanan.pelanggan', function($q) use ($search) {
                      $q->where('nama', 'like', "%{$search}%");
                  });
        }

        $multimedias = $query->paginate(10);

        return view('multimedias.index', compact('multimedias'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $pesanans = Pesanan::with('pelanggan')->get();
        return view('multimedias.create', compact('pesanans'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'pesanan_id' => 'required|exists:pesanans,id',
            'file' => 'required|file|mimes:jpeg,png,jpg,gif,mp4,avi,mov|max:20480', // 20MB max
            'kategori' => 'required|in:preview,hasil_edit,raw',
            'deskripsi' => 'nullable|string',
        ]);

        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('multimedia', $filename, 'public');

            Multimedia::create([
                'pesanan_id' => $request->pesanan_id,
                'nama_file' => $filename,
                'path_file' => $path,
                'tipe_file' => $file->getClientMimeType(),
                'ukuran_file' => $file->getSize(),
                'kategori' => $request->kategori,
                'deskripsi' => $request->deskripsi,
            ]);

            return redirect()->route('multimedias.index')
                            ->with('success', 'File multimedia berhasil diupload.');
        }

        return back()->with('error', 'Gagal mengupload file.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
