<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Multimedia;
use App\Models\Pesanan;
use Illuminate\Support\Facades\Storage;

class FinalPhotoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Pastikan folder multimedia ada
        if (!Storage::disk('public')->exists('multimedia')) {
            Storage::disk('public')->makeDirectory('multimedia');
        }

        // Ambil pesanan yang ada
        $pesanans = Pesanan::all();
        if ($pesanans->count() == 0) {
            $this->command->info('Tidak ada pesanan. Final photo seeder dilewati.');
            return;
        }

        // 1 foto terakhir untuk melengkapi jadi 8 foto yang pas
        $finalPhoto = [
            [
                'pesanan_id' => $pesanans->first()->id,
                'nama_file' => 'Creative Studio Portrait Art',
                'path_file' => 'multimedia/creative_studio_portrait.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 2048000,
                'kategori' => 'hasil_edit',
                'deskripsi' => 'Portrait kreatif di studio dengan lighting artistik dan komposisi yang unik. Hasil editing yang memukau.',
                'url_source' => 'https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?w=800&h=600&fit=crop'
            ]
        ];

        foreach ($finalPhoto as $data) {
            // Download dan simpan foto dari Unsplash
            if (isset($data['url_source'])) {
                try {
                    $imageContent = file_get_contents($data['url_source']);
                    if ($imageContent !== false) {
                        Storage::disk('public')->put($data['path_file'], $imageContent);
                        $this->command->info("Downloaded: {$data['nama_file']}");
                    }
                } catch (\Exception $e) {
                    $this->command->warn("Failed to download: {$data['nama_file']}");
                }
                unset($data['url_source']);
            }

            Multimedia::create($data);
        }

        $this->command->info('Final photo seeder completed! Now we have perfect 8+ photos for gallery.');
    }
}
