<?php

namespace App\Http\Controllers;

use App\Models\Pemotretan;
use App\Models\Pesanan;
use App\Models\Karyawan;
use Illuminate\Http\Request;

class PemotretanController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Pemotretan::with(['pesanan.pelanggan', 'karyawan']);

        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where('lokasi', 'like', "%{$search}%")
                  ->orWhere('jenis_foto', 'like', "%{$search}%")
                  ->orWhereHas('pesanan.pelanggan', function($q) use ($search) {
                      $q->where('nama', 'like', "%{$search}%");
                  });
        }

        $pemotretans = $query->orderBy('tanggal_pemotretan', 'desc')->paginate(10);

        return view('pemotretans.index', compact('pemotretans'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $pesanans = Pesanan::with('pelanggan')->get();
        $karyawans = Karyawan::where('posisi', 'fotografer')->where('status', 'aktif')->get();
        return view('pemotretans.create', compact('pesanans', 'karyawans'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'pesanan_id' => 'required|exists:pesanans,id',
            'karyawan_id' => 'required|exists:karyawans,id',
            'tanggal_pemotretan' => 'required|date',
            'lokasi' => 'required|string|max:255',
            'jenis_foto' => 'required|string|max:255',
            'durasi' => 'required|integer|min:1',
            'status' => 'required|in:terjadwal,berlangsung,selesai,dibatalkan',
            'catatan' => 'nullable|string',
        ]);

        Pemotretan::create($request->all());

        return redirect()->route('pemotretans.index')
                        ->with('success', 'Pemotretan berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Pemotretan $pemotretan)
    {
        $pemotretan->load(['pesanan.pelanggan', 'karyawan']);
        return view('pemotretans.show', compact('pemotretan'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Pemotretan $pemotretan)
    {
        $pesanans = Pesanan::with('pelanggan')->get();
        $karyawans = Karyawan::where('posisi', 'fotografer')->where('status', 'aktif')->get();
        return view('pemotretans.edit', compact('pemotretan', 'pesanans', 'karyawans'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Pemotretan $pemotretan)
    {
        $request->validate([
            'pesanan_id' => 'required|exists:pesanans,id',
            'karyawan_id' => 'required|exists:karyawans,id',
            'tanggal_pemotretan' => 'required|date',
            'lokasi' => 'required|string|max:255',
            'jenis_foto' => 'required|string|max:255',
            'durasi' => 'required|integer|min:1',
            'status' => 'required|in:terjadwal,berlangsung,selesai,dibatalkan',
            'catatan' => 'nullable|string',
        ]);

        $pemotretan->update($request->all());

        return redirect()->route('pemotretans.index')
                        ->with('success', 'Pemotretan berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Pemotretan $pemotretan)
    {
        $pemotretan->delete();

        return redirect()->route('pemotretans.index')
                        ->with('success', 'Pemotretan berhasil dihapus.');
    }
}
