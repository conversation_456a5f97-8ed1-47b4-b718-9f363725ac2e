<x-user-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Pesan Layanan Foto') }}
        </h2>
    </x-slot>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="card hover-lift">
            <div class="card-header p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 gradient-text">Pesan Layanan Foto</h3>
                        <p class="text-gray-600 mt-1">Pilih layanan yang Anda inginkan dan isi data diri</p>
                    </div>
                    <a href="{{ route('user.pesanans.index') }}" class="btn btn-secondary px-6 py-3">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Form Card -->
        <div class="card">
            <div class="p-8">
                <form action="{{ route('user.pesanans.store') }}" method="POST" class="space-y-8" id="pesananForm">
                    @csrf
                    
                    <!-- Step 1: Pilih Layanan -->
                    <div class="space-y-6">
                        <div class="border-b border-gray-200 pb-4">
                            <h4 class="text-lg font-semibold text-gray-900 flex items-center">
                                <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">1</span>
                                Pilih Layanan Foto
                            </h4>
                            <p class="text-gray-600 mt-1 ml-11">Pilih jenis layanan foto yang Anda inginkan</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($layanans as $layanan)
                                <div class="layanan-card border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors duration-200" 
                                     data-layanan-id="{{ $layanan->id }}"
                                     data-harga-dasar="{{ $layanan->harga_dasar }}"
                                     data-harga-premium="{{ $layanan->harga_premium }}">
                                    
                                    @if($layanan->gambar_layanan)
                                        <img src="{{ asset('storage/' . $layanan->gambar_layanan) }}" 
                                             alt="{{ $layanan->nama_layanan }}"
                                             class="w-full h-32 object-cover rounded-lg mb-3">
                                    @endif
                                    
                                    <h5 class="font-semibold text-gray-900 mb-2">{{ $layanan->nama_layanan }}</h5>
                                    <p class="text-sm text-gray-600 mb-3">{{ Str::limit($layanan->deskripsi, 80) }}</p>
                                    
                                    <div class="space-y-1">
                                        <p class="text-lg font-bold text-blue-600">{{ $layanan->harga_format }}</p>
                                        @if($layanan->harga_premium)
                                            <p class="text-sm text-gray-600">Premium: {{ $layanan->harga_premium_format }}</p>
                                        @endif
                                        <p class="text-xs text-gray-500">{{ $layanan->durasi_jam }} jam</p>
                                    </div>
                                    
                                    <input type="radio" name="layanan_id" value="{{ $layanan->id }}" class="hidden layanan-radio">
                                </div>
                            @endforeach
                        </div>
                        @error('layanan_id')
                            <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Step 2: Pilih Paket -->
                    <div class="space-y-6" id="paketSection" style="display: none;">
                        <div class="border-b border-gray-200 pb-4">
                            <h4 class="text-lg font-semibold text-gray-900 flex items-center">
                                <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">2</span>
                                Pilih Paket
                            </h4>
                            <p class="text-gray-600 mt-1 ml-11">Pilih paket yang sesuai dengan kebutuhan Anda</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="paket-card border-2 border-gray-200 rounded-lg p-6 cursor-pointer hover:border-blue-500 transition-colors duration-200" 
                                 data-paket="dasar">
                                <div class="flex justify-between items-start mb-4">
                                    <h5 class="text-xl font-semibold text-gray-900">Paket Dasar</h5>
                                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">Populer</span>
                                </div>
                                <p class="text-2xl font-bold text-blue-600 mb-4" id="hargaDasar">-</p>
                                <ul class="space-y-2 text-sm text-gray-600" id="fiturDasar">
                                    <!-- Fitur akan diisi via JavaScript -->
                                </ul>
                                <input type="radio" name="paket_pilihan" value="dasar" class="hidden paket-radio">
                            </div>

                            <div class="paket-card border-2 border-gray-200 rounded-lg p-6 cursor-pointer hover:border-blue-500 transition-colors duration-200" 
                                 data-paket="premium" id="paketPremium" style="display: none;">
                                <div class="flex justify-between items-start mb-4">
                                    <h5 class="text-xl font-semibold text-gray-900">Paket Premium</h5>
                                    <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">Terbaik</span>
                                </div>
                                <p class="text-2xl font-bold text-purple-600 mb-4" id="hargaPremium">-</p>
                                <ul class="space-y-2 text-sm text-gray-600" id="fiturPremium">
                                    <!-- Fitur akan diisi via JavaScript -->
                                </ul>
                                <input type="radio" name="paket_pilihan" value="premium" class="hidden paket-radio">
                            </div>
                        </div>
                        @error('paket_pilihan')
                            <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Step 3: Data Diri -->
                    <div class="space-y-6" id="dataDiriSection" style="display: none;">
                        <div class="border-b border-gray-200 pb-4">
                            <h4 class="text-lg font-semibold text-gray-900 flex items-center">
                                <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">3</span>
                                Data Diri
                            </h4>
                            <p class="text-gray-600 mt-1 ml-11">Isi data diri Anda untuk pemesanan</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Nama -->
                            <div class="space-y-2">
                                <label for="nama_pelanggan" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    Nama Lengkap
                                </label>
                                <input type="text" id="nama_pelanggan" name="nama_pelanggan" value="{{ old('nama_pelanggan') }}" required
                                       placeholder="Masukkan nama lengkap Anda"
                                       class="form-input w-full">
                                @error('nama_pelanggan')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Email -->
                            <div class="space-y-2">
                                <label for="email_pelanggan" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                    </svg>
                                    Email
                                </label>
                                <input type="email" id="email_pelanggan" name="email_pelanggan" value="{{ old('email_pelanggan') }}" required
                                       placeholder="<EMAIL>"
                                       class="form-input w-full">
                                @error('email_pelanggan')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Kontak -->
                            <div class="space-y-2">
                                <label for="kontak_pelanggan" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                    Nomor WhatsApp
                                </label>
                                <input type="text" id="kontak_pelanggan" name="kontak_pelanggan" value="{{ old('kontak_pelanggan') }}" required
                                       placeholder="08123456789"
                                       class="form-input w-full">
                                @error('kontak_pelanggan')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Tanggal Pesanan -->
                            <div class="space-y-2">
                                <label for="tanggal_pesanan" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Tanggal Diinginkan
                                </label>
                                <input type="date" id="tanggal_pesanan" name="tanggal_pesanan" value="{{ old('tanggal_pesanan') }}" required
                                       min="{{ date('Y-m-d', strtotime('+1 day')) }}"
                                       class="form-input w-full">
                                @error('tanggal_pesanan')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Alamat -->
                        <div class="space-y-2">
                            <label for="alamat_pelanggan" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Alamat Lengkap
                            </label>
                            <textarea id="alamat_pelanggan" name="alamat_pelanggan" rows="3" required
                                      placeholder="Masukkan alamat lengkap Anda"
                                      class="form-input w-full resize-none">{{ old('alamat_pelanggan') }}</textarea>
                            @error('alamat_pelanggan')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Catatan -->
                        <div class="space-y-2">
                            <label for="catatan" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Catatan Khusus (Opsional)
                            </label>
                            <textarea id="catatan" name="catatan" rows="3"
                                      placeholder="Tambahkan catatan atau permintaan khusus..."
                                      class="form-input w-full resize-none">{{ old('catatan') }}</textarea>
                            @error('catatan')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Summary & Submit -->
                    <div class="space-y-6" id="summarySection" style="display: none;">
                        <div class="border-b border-gray-200 pb-4">
                            <h4 class="text-lg font-semibold text-gray-900 flex items-center">
                                <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">4</span>
                                Ringkasan Pesanan
                            </h4>
                            <p class="text-gray-600 mt-1 ml-11">Periksa kembali detail pesanan Anda</p>
                        </div>

                        <div class="bg-gray-50 rounded-lg p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h5 class="font-semibold text-gray-900 mb-3">Detail Layanan</h5>
                                    <div class="space-y-2 text-sm">
                                        <p><span class="text-gray-600">Layanan:</span> <span id="summaryLayanan">-</span></p>
                                        <p><span class="text-gray-600">Paket:</span> <span id="summaryPaket">-</span></p>
                                        <p><span class="text-gray-600">Harga:</span> <span id="summaryHarga" class="font-bold text-blue-600">-</span></p>
                                    </div>
                                </div>
                                <div>
                                    <h5 class="font-semibold text-gray-900 mb-3">Data Pelanggan</h5>
                                    <div class="space-y-2 text-sm">
                                        <p><span class="text-gray-600">Nama:</span> <span id="summaryNama">-</span></p>
                                        <p><span class="text-gray-600">Email:</span> <span id="summaryEmail">-</span></p>
                                        <p><span class="text-gray-600">Tanggal:</span> <span id="summaryTanggal">-</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                            <a href="{{ route('user.pesanans.index') }}" 
                               class="btn btn-secondary px-8 py-3">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Batal
                            </a>
                            <button type="submit" class="btn btn-primary px-8 py-3">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Pesan Sekarang
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        let selectedLayanan = null;
        let selectedPaket = null;

        // Layanan selection
        document.querySelectorAll('.layanan-card').forEach(card => {
            card.addEventListener('click', function() {
                // Remove previous selection
                document.querySelectorAll('.layanan-card').forEach(c => {
                    c.classList.remove('border-blue-500', 'bg-blue-50');
                    c.querySelector('.layanan-radio').checked = false;
                });
                
                // Select current
                this.classList.add('border-blue-500', 'bg-blue-50');
                this.querySelector('.layanan-radio').checked = true;
                
                selectedLayanan = {
                    id: this.dataset.layananId,
                    nama: this.querySelector('h5').textContent,
                    hargaDasar: parseInt(this.dataset.hargaDasar),
                    hargaPremium: this.dataset.hargaPremium ? parseInt(this.dataset.hargaPremium) : null
                };
                
                showPaketSection();
            });
        });

        // Paket selection
        document.querySelectorAll('.paket-card').forEach(card => {
            card.addEventListener('click', function() {
                // Remove previous selection
                document.querySelectorAll('.paket-card').forEach(c => {
                    c.classList.remove('border-blue-500', 'bg-blue-50');
                    c.querySelector('.paket-radio').checked = false;
                });
                
                // Select current
                this.classList.add('border-blue-500', 'bg-blue-50');
                this.querySelector('.paket-radio').checked = true;
                
                selectedPaket = this.dataset.paket;
                showDataDiriSection();
            });
        });

        function showPaketSection() {
            // Update harga
            document.getElementById('hargaDasar').textContent = 'Rp ' + selectedLayanan.hargaDasar.toLocaleString('id-ID');
            
            if (selectedLayanan.hargaPremium) {
                document.getElementById('hargaPremium').textContent = 'Rp ' + selectedLayanan.hargaPremium.toLocaleString('id-ID');
                document.getElementById('paketPremium').style.display = 'block';
            } else {
                document.getElementById('paketPremium').style.display = 'none';
            }
            
            document.getElementById('paketSection').style.display = 'block';
            document.getElementById('paketSection').scrollIntoView({ behavior: 'smooth' });
        }

        function showDataDiriSection() {
            document.getElementById('dataDiriSection').style.display = 'block';
            document.getElementById('dataDiriSection').scrollIntoView({ behavior: 'smooth' });
        }

        // Form validation and summary
        document.getElementById('pesananForm').addEventListener('input', function() {
            updateSummary();
        });

        function updateSummary() {
            if (selectedLayanan && selectedPaket) {
                const harga = selectedPaket === 'premium' && selectedLayanan.hargaPremium 
                    ? selectedLayanan.hargaPremium 
                    : selectedLayanan.hargaDasar;
                
                document.getElementById('summaryLayanan').textContent = selectedLayanan.nama;
                document.getElementById('summaryPaket').textContent = selectedPaket === 'premium' ? 'Premium Package' : 'Basic Package';
                document.getElementById('summaryHarga').textContent = 'Rp ' + harga.toLocaleString('id-ID');
                
                // Update customer data
                document.getElementById('summaryNama').textContent = document.getElementById('nama_pelanggan').value || '-';
                document.getElementById('summaryEmail').textContent = document.getElementById('email_pelanggan').value || '-';
                document.getElementById('summaryTanggal').textContent = document.getElementById('tanggal_pesanan').value || '-';
                
                // Show summary if all required fields are filled
                const requiredFields = ['nama_pelanggan', 'email_pelanggan', 'kontak_pelanggan', 'alamat_pelanggan', 'tanggal_pesanan'];
                const allFilled = requiredFields.every(field => document.getElementById(field).value.trim() !== '');
                
                if (allFilled) {
                    document.getElementById('summarySection').style.display = 'block';
                }
            }
        }
    </script>
</x-user-sidebar-layout>
