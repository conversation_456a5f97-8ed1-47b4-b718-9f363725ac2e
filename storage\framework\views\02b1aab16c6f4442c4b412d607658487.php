<?php if (isset($component)) { $__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-sidebar-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('user-sidebar-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Galeri Foto')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="card hover-lift">
            <div class="card-header p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 gradient-text">Galeri Foto & Video</h3>
                        <p class="text-gray-600 mt-1">Koleksi hasil pemotretan dan video Anda</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="<?php echo e(route('user.multimedias.create')); ?>" class="btn btn-primary px-6 py-3">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Upload File
                        </a>
                        <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">Galeri Pribadi</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Card -->
        <div class="card">
            <div class="p-6">
                <form method="GET" action="<?php echo e(route('user.multimedias.index')); ?>" class="flex items-center space-x-4">
                    <div class="flex-1">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <input type="text" name="search" value="<?php echo e(request('search')); ?>" 
                                   placeholder="Cari berdasarkan nama file atau kategori..."
                                   class="form-input pl-10 w-full">
                        </div>
                    </div>
                    <button type="submit" class="btn btn-secondary px-6 py-3">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Cari
                    </button>
                </form>
            </div>
        </div>

        <!-- Gallery Grid -->
        <?php if($multimedias->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <?php $__currentLoopData = $multimedias; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $multimedia): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="card hover-lift">
                        <div class="relative">
                            <img src="<?php echo e(asset('storage/' . $multimedia->path_file)); ?>"
                                 alt="<?php echo e($multimedia->nama_file); ?>"
                                 class="w-full h-48 object-cover rounded-t-lg">
                            
                            <!-- Category Badge -->
                            <div class="absolute top-2 right-2">
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    <?php if($multimedia->kategori == 'preview'): ?> bg-green-100 text-green-800
                                    <?php elseif($multimedia->kategori == 'hasil_edit'): ?> bg-blue-100 text-blue-800
                                    <?php else: ?> bg-purple-100 text-purple-800 <?php endif; ?>">
                                    <?php if($multimedia->kategori == 'preview'): ?> Preview
                                    <?php elseif($multimedia->kategori == 'hasil_edit'): ?> Final
                                    <?php else: ?> RAW <?php endif; ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="p-4">
                            <h4 class="font-semibold text-gray-900 truncate"><?php echo e($multimedia->nama_file); ?></h4>
                            <p class="text-sm text-gray-600 mt-1"><?php echo e($multimedia->pesanan->pelanggan->nama ?? 'N/A'); ?></p>
                            <p class="text-xs text-gray-500 mt-1"><?php echo e(number_format($multimedia->ukuran_file / 1024, 1)); ?> KB</p>
                            
                            <?php if($multimedia->deskripsi): ?>
                                <p class="text-sm text-gray-600 mt-2 line-clamp-2"><?php echo e($multimedia->deskripsi); ?></p>
                            <?php endif; ?>
                            
                            <div class="flex items-center justify-between mt-4">
                                <div class="flex space-x-2">
                                    <a href="<?php echo e(route('user.multimedias.show', $multimedia)); ?>" 
                                       class="inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        Lihat
                                    </a>
                                    <a href="<?php echo e(asset('storage/' . $multimedia->path_file)); ?>" download
                                       class="inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors duration-200">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        Download
                                    </a>
                                </div>
                                <div class="flex space-x-1">
                                    <a href="<?php echo e(route('user.multimedias.edit', $multimedia)); ?>" 
                                       class="inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 transition-colors duration-200">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </a>
                                    <form action="<?php echo e(route('user.multimedias.destroy', $multimedia)); ?>" method="POST" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" 
                                                onclick="return confirm('Yakin ingin menghapus file ini?')"
                                                class="inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 transition-colors duration-200">
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Pagination -->
            <div class="card">
                <div class="p-6">
                    <?php echo e($multimedias->links()); ?>

                </div>
            </div>
        <?php else: ?>
            <!-- Empty State -->
            <div class="card">
                <div class="p-12 text-center">
                    <svg class="w-24 h-24 text-gray-400 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Galeri Masih Kosong</h3>
                    <p class="text-gray-600 mb-6">Belum ada foto atau video yang diupload. Mulai upload file pertama Anda!</p>
                    <a href="<?php echo e(route('user.multimedias.create')); ?>" class="btn btn-primary px-8 py-3">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Upload File Pertama
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14)): ?>
<?php $attributes = $__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14; ?>
<?php unset($__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14)): ?>
<?php $component = $__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14; ?>
<?php unset($__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\TokoPhotoStudio\resources\views/user/multimedias/index.blade.php ENDPATH**/ ?>