<x-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Tambah Template') }}
        </h2>
    </x-slot>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="card hover-lift">
            <div class="card-header p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 gradient-text">Buat Template Baru</h3>
                        <p class="text-gray-600 mt-1">Buat template editing dan preset kamera</p>
                    </div>
                    <a href="{{ route('templates.index') }}" class="btn btn-secondary px-6 py-3">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Form Card -->
        <div class="card">
            <div class="p-8">
                <form action="{{ route('templates.store') }}" method="POST" class="space-y-8">
                    @csrf
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Nama Template -->
                        <div class="space-y-2">
                            <label for="nama_template" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                Nama Template
                            </label>
                            <input type="text" id="nama_template" name="nama_template" value="{{ old('nama_template') }}" required
                                   placeholder="Masukkan nama template"
                                   class="form-input w-full">
                            @error('nama_template')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Kategori -->
                        <div class="space-y-2">
                            <label for="kategori" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                                Kategori
                            </label>
                            <select id="kategori" name="kategori" required class="form-input w-full">
                                <option value="">Pilih Kategori</option>
                                <option value="Portrait" {{ old('kategori') == 'Portrait' ? 'selected' : '' }}>Portrait</option>
                                <option value="Wedding" {{ old('kategori') == 'Wedding' ? 'selected' : '' }}>Wedding</option>
                                <option value="Product" {{ old('kategori') == 'Product' ? 'selected' : '' }}>Product</option>
                                <option value="Landscape" {{ old('kategori') == 'Landscape' ? 'selected' : '' }}>Landscape</option>
                                <option value="Fashion" {{ old('kategori') == 'Fashion' ? 'selected' : '' }}>Fashion</option>
                                <option value="Event" {{ old('kategori') == 'Event' ? 'selected' : '' }}>Event</option>
                                <option value="Studio" {{ old('kategori') == 'Studio' ? 'selected' : '' }}>Studio</option>
                                <option value="Outdoor" {{ old('kategori') == 'Outdoor' ? 'selected' : '' }}>Outdoor</option>
                            </select>
                            @error('kategori')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Deskripsi -->
                    <div class="space-y-2">
                        <label for="deskripsi" class="form-label">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
                            </svg>
                            Deskripsi
                        </label>
                        <textarea id="deskripsi" name="deskripsi" rows="4" 
                                  placeholder="Jelaskan kegunaan dan karakteristik template ini"
                                  class="form-input w-full resize-none">{{ old('deskripsi') }}</textarea>
                        @error('deskripsi')
                            <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Preset Settings -->
                    <div class="space-y-2">
                        <label for="preset_settings" class="form-label">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            Preset Settings (JSON)
                        </label>
                        <textarea id="preset_settings" name="preset_settings" rows="8" required
                                  placeholder='Masukkan pengaturan dalam format JSON, contoh:
{
  "exposure": "+0.5",
  "contrast": "20",
  "highlights": "-50",
  "shadows": "+30",
  "whites": "10",
  "blacks": "-10",
  "clarity": "15",
  "vibrance": "20",
  "saturation": "10"
}'
                                  class="form-input w-full resize-none font-mono text-sm">{{ old('preset_settings', '{\n  "exposure": "0",\n  "contrast": "0",\n  "highlights": "0",\n  "shadows": "0",\n  "whites": "0",\n  "blacks": "0",\n  "clarity": "0",\n  "vibrance": "0",\n  "saturation": "0"\n}') }}</textarea>
                        @error('preset_settings')
                            <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                        @enderror
                        <p class="text-sm text-gray-600">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Masukkan pengaturan dalam format JSON yang valid. Gunakan nilai numerik untuk setiap parameter.
                        </p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                        <a href="{{ route('templates.index') }}" 
                           class="btn btn-secondary px-8 py-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Batal
                        </a>
                        <button type="submit" class="btn btn-primary px-8 py-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Simpan Template
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // JSON validation
        document.getElementById('preset_settings').addEventListener('blur', function() {
            try {
                JSON.parse(this.value);
                this.classList.remove('border-red-500');
                this.classList.add('border-green-500');
            } catch (e) {
                this.classList.remove('border-green-500');
                this.classList.add('border-red-500');
            }
        });
    </script>
</x-sidebar-layout>
