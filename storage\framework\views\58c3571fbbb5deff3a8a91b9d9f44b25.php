<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Detail Pelanggan')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold">Detail Pelanggan</h3>
                        <div class="flex gap-2">
                            <a href="<?php echo e(route('pelanggans.edit', $pelanggan)); ?>" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                                Edit
                            </a>
                            <a href="<?php echo e(route('pelanggans.index')); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Kembali
                            </a>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-700 mb-3">Informasi Pelanggan</h4>
                            
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">ID</label>
                                    <p class="text-gray-900"><?php echo e($pelanggan->id); ?></p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Nama</label>
                                    <p class="text-gray-900"><?php echo e($pelanggan->nama); ?></p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Email</label>
                                    <p class="text-gray-900"><?php echo e($pelanggan->email); ?></p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Kontak</label>
                                    <p class="text-gray-900"><?php echo e($pelanggan->kontak); ?></p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Alamat</label>
                                    <p class="text-gray-900"><?php echo e($pelanggan->alamat ?: 'Tidak ada alamat'); ?></p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Tanggal Daftar</label>
                                    <p class="text-gray-900"><?php echo e($pelanggan->created_at->format('d/m/Y H:i')); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-700 mb-3">Riwayat Transaksi</h4>
                            
                            <?php if($pelanggan->pesanans->count() > 0): ?>
                                <div class="space-y-2">
                                    <?php $__currentLoopData = $pelanggan->pesanans->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pesanan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="bg-white p-3 rounded border">
                                            <p class="font-medium"><?php echo e($pesanan->jenis_layanan); ?></p>
                                            <p class="text-sm text-gray-600"><?php echo e($pesanan->paket); ?> - Rp <?php echo e(number_format($pesanan->harga, 0, ',', '.')); ?></p>
                                            <p class="text-xs text-gray-500"><?php echo e($pesanan->tanggal_pesanan->format('d/m/Y')); ?></p>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    
                                    <?php if($pelanggan->pesanans->count() > 5): ?>
                                        <p class="text-sm text-gray-500">Dan <?php echo e($pelanggan->pesanans->count() - 5); ?> pesanan lainnya...</p>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <p class="text-gray-500">Belum ada riwayat pesanan</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\TokoPhotoStudio\resources\views/pelanggans/show.blade.php ENDPATH**/ ?>