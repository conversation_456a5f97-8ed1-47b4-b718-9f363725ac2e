<?php if (isset($component)) { $__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-sidebar-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('user-sidebar-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Booking Studio Foto')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="space-y-6">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg">
            <div class="p-6 text-white">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-bold">📸 Booking Studio Foto</h3>
                        <p class="text-blue-100 mt-1">Pilih paket studio yang sesuai kebutuhan Anda</p>
                    </div>
                    <a href="<?php echo e(route('user.bookings.index')); ?>" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all duration-200">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Studio Packages -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <?php $__currentLoopData = $studioPackages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <!-- Package Image -->
                    <div class="h-48 overflow-hidden">
                        <img src="<?php echo e($package['image']); ?>" 
                             alt="<?php echo e($package['name']); ?>"
                             class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
                    </div>
                    
                    <!-- Package Info -->
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2"><?php echo e($package['name']); ?></h3>
                        <p class="text-gray-600 mb-4"><?php echo e($package['description']); ?></p>
                        
                        <!-- Price -->
                        <div class="bg-blue-50 rounded-lg p-4 mb-4">
                            <div class="flex justify-between items-center">
                                <span class="text-2xl font-bold text-blue-600">Rp <?php echo e(number_format($package['price'], 0, ',', '.')); ?></span>
                                <span class="text-sm text-gray-600"><?php echo e($package['duration']); ?></span>
                            </div>
                            <p class="text-sm text-gray-500 mt-1"><?php echo e($package['capacity']); ?></p>
                        </div>
                        
                        <!-- Features -->
                        <div class="space-y-2 mb-6">
                            <?php $__currentLoopData = $package['features']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <?php echo e($feature); ?>

                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        
                        <!-- Book Button -->
                        <button type="button" 
                                onclick="openBookingModal(<?php echo e(json_encode($package)); ?>)"
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200">
                            📅 Book Sekarang
                        </button>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Booking Modal -->
        <div id="bookingModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
            <div class="bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
                <form action="<?php echo e(route('user.bookings.store')); ?>" method="POST" id="bookingForm">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" id="selectedPackage" name="ruang_studio">
                    <input type="hidden" id="packagePrice" name="package_price">
                    
                    <!-- Modal Header -->
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-xl font-bold text-gray-900" id="modalTitle">Booking Studio</h3>
                            <button type="button" onclick="closeBookingModal()" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <p class="text-gray-600 mt-1" id="modalDescription">Isi data booking Anda</p>
                    </div>

                    <!-- Modal Body -->
                    <div class="p-6 space-y-4">
                        <!-- Package Summary -->
                        <div class="bg-gray-50 rounded-lg p-4" id="packageSummary">
                            <!-- Will be filled by JavaScript -->
                        </div>
                        
                        <!-- Booking Date -->
                        <div>
                            <label for="tanggal_booking" class="block text-sm font-medium text-gray-700 mb-2">
                                📅 Tanggal Booking
                            </label>
                            <input type="date" id="tanggal_booking" name="tanggal_booking" required
                                   min="<?php echo e(date('Y-m-d', strtotime('+1 day'))); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <?php $__errorArgs = ['tanggal_booking'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-600 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        
                        <!-- Time -->
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="jam_mulai" class="block text-sm font-medium text-gray-700 mb-2">
                                    ⏰ Jam Mulai
                                </label>
                                <select id="jam_mulai" name="jam_mulai" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Pilih Jam</option>
                                    <?php for($i = 8; $i <= 20; $i++): ?>
                                        <option value="<?php echo e(sprintf('%02d:00', $i)); ?>"><?php echo e(sprintf('%02d:00', $i)); ?></option>
                                        <option value="<?php echo e(sprintf('%02d:30', $i)); ?>"><?php echo e(sprintf('%02d:30', $i)); ?></option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                            <div>
                                <label for="jam_selesai" class="block text-sm font-medium text-gray-700 mb-2">
                                    ⏰ Jam Selesai
                                </label>
                                <select id="jam_selesai" name="jam_selesai" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Pilih Jam</option>
                                    <?php for($i = 9; $i <= 22; $i++): ?>
                                        <option value="<?php echo e(sprintf('%02d:00', $i)); ?>"><?php echo e(sprintf('%02d:00', $i)); ?></option>
                                        <option value="<?php echo e(sprintf('%02d:30', $i)); ?>"><?php echo e(sprintf('%02d:30', $i)); ?></option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Event Type -->
                        <div>
                            <label for="jenis_acara" class="block text-sm font-medium text-gray-700 mb-2">
                                🎭 Jenis Acara
                            </label>
                            <select id="jenis_acara" name="jenis_acara" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Pilih Jenis Acara</option>
                                <option value="Portrait Session">Portrait Session</option>
                                <option value="Wedding Photography">Wedding Photography</option>
                                <option value="Pre-Wedding">Pre-Wedding</option>
                                <option value="Family Photo">Family Photo</option>
                                <option value="Corporate">Corporate</option>
                                <option value="Fashion">Fashion</option>
                                <option value="Product">Product</option>
                                <option value="Lainnya">Lainnya</option>
                            </select>
                        </div>
                        
                        <!-- Participants -->
                        <div>
                            <label for="jumlah_peserta" class="block text-sm font-medium text-gray-700 mb-2">
                                👥 Jumlah Peserta
                            </label>
                            <input type="number" id="jumlah_peserta" name="jumlah_peserta" required min="1" max="20" value="1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <!-- Customer Data -->
                        <div class="border-t pt-4">
                            <h4 class="font-medium text-gray-900 mb-4">👤 Data Diri</h4>
                            
                            <div class="space-y-4">
                                <div>
                                    <label for="nama_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                                        Nama Lengkap
                                    </label>
                                    <input type="text" id="nama_pelanggan" name="nama_pelanggan" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                
                                <div>
                                    <label for="email_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                                        Email
                                    </label>
                                    <input type="email" id="email_pelanggan" name="email_pelanggan" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                
                                <div>
                                    <label for="kontak_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                                        WhatsApp
                                    </label>
                                    <input type="text" id="kontak_pelanggan" name="kontak_pelanggan" required
                                           placeholder="08123456789"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                
                                <div>
                                    <label for="alamat_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                                        Alamat
                                    </label>
                                    <textarea id="alamat_pelanggan" name="alamat_pelanggan" required rows="2"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"></textarea>
                                </div>
                                
                                <div>
                                    <label for="catatan" class="block text-sm font-medium text-gray-700 mb-2">
                                        Catatan (Opsional)
                                    </label>
                                    <textarea id="catatan" name="catatan" rows="2"
                                              placeholder="Permintaan khusus atau catatan tambahan..."
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Modal Footer -->
                    <div class="p-6 border-t border-gray-200">
                        <div class="flex space-x-3">
                            <button type="button" onclick="closeBookingModal()"
                                    class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-3 px-4 rounded-lg transition-colors duration-200">
                                Batal
                            </button>
                            <button type="submit"
                                    class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200">
                                📅 Booking Sekarang
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function openBookingModal(package) {
            document.getElementById('bookingModal').classList.remove('hidden');
            document.getElementById('modalTitle').textContent = 'Booking ' + package.name;
            document.getElementById('modalDescription').textContent = package.description;
            document.getElementById('selectedPackage').value = package.name;
            document.getElementById('packagePrice').value = package.price;
            
            // Update package summary
            document.getElementById('packageSummary').innerHTML = `
                <div class="flex justify-between items-center">
                    <div>
                        <h4 class="font-medium text-gray-900">${package.name}</h4>
                        <p class="text-sm text-gray-600">${package.description}</p>
                    </div>
                    <div class="text-right">
                        <p class="text-xl font-bold text-blue-600">Rp ${package.price.toLocaleString('id-ID')}</p>
                        <p class="text-sm text-gray-500">${package.duration} • ${package.capacity}</p>
                    </div>
                </div>
            `;
            
            document.body.style.overflow = 'hidden';
        }

        function closeBookingModal() {
            document.getElementById('bookingModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
            document.getElementById('bookingForm').reset();
        }

        // Close modal when clicking outside
        document.getElementById('bookingModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeBookingModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeBookingModal();
            }
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14)): ?>
<?php $attributes = $__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14; ?>
<?php unset($__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14)): ?>
<?php $component = $__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14; ?>
<?php unset($__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\TokoPhotoStudio\resources\views/user/bookings/create.blade.php ENDPATH**/ ?>