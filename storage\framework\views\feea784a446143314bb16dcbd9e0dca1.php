<?php if (isset($component)) { $__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-sidebar-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('user-sidebar-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Booking Studio Foto')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="space-y-6 fade-in">
        <!-- Header Card -->
        <div class="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-2xl shadow-2xl overflow-hidden">
            <div class="p-8 text-white">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-3xl font-bold mb-2">🏢 Booking Studio Foto</h3>
                        <p class="text-blue-100 text-lg">Pilih studio yang Anda inginkan dan isi data diri</p>
                        <div class="flex items-center space-x-4 mt-4 text-sm text-blue-100">
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Peralatan lengkap</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Lighting profesional</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Booking fleksibel</span>
                            </div>
                        </div>
                    </div>
                    <a href="<?php echo e(route('user.bookings.index')); ?>" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 backdrop-blur-sm">
                        <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Form Card -->
        <div class="card">
            <div class="p-8">
                <form action="<?php echo e(route('user.bookings.store')); ?>" method="POST" class="space-y-8" id="bookingForm">
                    <?php echo csrf_field(); ?>
                    
                    <!-- Step 1: Pilih Studio -->
                    <div class="space-y-6">
                        <div class="border-b border-gray-200 pb-4">
                            <h4 class="text-lg font-semibold text-gray-900 flex items-center">
                                <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">1</span>
                                Pilih Studio Foto
                            </h4>
                            <p class="text-gray-600 mt-1 ml-11">Pilih jenis studio foto yang Anda inginkan</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <?php $__currentLoopData = $studioRooms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $room): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="studio-card group border-2 border-gray-200 rounded-xl p-6 cursor-pointer hover:border-blue-500 hover:shadow-xl transition-all duration-300 bg-white"
                                     data-studio="<?php echo e($room['name']); ?>"
                                     data-price="<?php echo e($room['price_per_hour']); ?>">

                                    <!-- Studio Image -->
                                    <div class="relative mb-4">
                                        <?php if(isset($room['studio_image'])): ?>
                                            <img src="<?php echo e($room['studio_image']); ?>"
                                                 alt="<?php echo e($room['name']); ?>"
                                                 class="w-full h-48 object-cover rounded-lg group-hover:scale-105 transition-transform duration-300">
                                        <?php else: ?>
                                            <div class="h-48 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 flex items-center justify-center relative rounded-lg group-hover:scale-105 transition-transform duration-300">
                                                <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Studio Badge -->
                                        <div class="absolute top-3 right-3">
                                            <span class="px-3 py-1 text-xs font-semibold rounded-full bg-white bg-opacity-95 text-gray-800 shadow-lg">
                                                👥 <?php echo e($room['capacity']); ?>

                                            </span>
                                        </div>

                                        <!-- Recommended Badge -->
                                        <?php if($index == 1): ?>
                                            <div class="absolute top-3 left-3">
                                                <span class="px-3 py-1 text-xs font-semibold rounded-full bg-gradient-to-r from-green-400 to-blue-500 text-white shadow-lg">
                                                    ⭐ Rekomendasi
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Studio Info -->
                                    <div class="space-y-3">
                                        <h5 class="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors"><?php echo e($room['name']); ?></h5>
                                        <p class="text-sm text-gray-600 line-clamp-3 leading-relaxed"><?php echo e($room['description']); ?></p>

                                        <!-- Pricing -->
                                        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 space-y-2">
                                            <div class="flex items-center justify-between">
                                                <span class="text-sm font-medium text-gray-700">Harga per jam</span>
                                                <span class="text-2xl font-bold text-blue-600">Rp <?php echo e(number_format($room['price_per_hour'], 0, ',', '.')); ?></span>
                                            </div>
                                            <div class="text-center pt-2 border-t border-gray-200">
                                                <span class="text-xs text-gray-500">👥 Kapasitas: <?php echo e($room['capacity']); ?> • ⏱️ Minimum 1 jam</span>
                                            </div>
                                        </div>

                                        <!-- Features Preview -->
                                        <div class="space-y-3">
                                            <p class="text-sm font-medium text-gray-700">Fasilitas Utama:</p>
                                            <div class="grid grid-cols-2 gap-1">
                                                <?php $__currentLoopData = array_slice($room['features'], 0, 4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="flex items-center space-x-1 text-xs text-gray-600">
                                                        <svg class="w-3 h-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                        </svg>
                                                        <span><?php echo e($feature); ?></span>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                            <?php if(count($room['features']) > 4): ?>
                                                <p class="text-xs text-blue-600 font-medium">+<?php echo e(count($room['features']) - 4); ?> fasilitas lainnya</p>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Sample Photos -->
                                        <?php if(isset($room['sample_photos']) && count($room['sample_photos']) > 0): ?>
                                            <div class="space-y-3">
                                                <p class="text-sm font-medium text-gray-700">Contoh Hasil Foto:</p>
                                                <div class="grid grid-cols-3 gap-2">
                                                    <?php $__currentLoopData = array_slice($room['sample_photos'], 0, 3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $photo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="relative group/photo cursor-pointer" onclick="openPhotoModal('<?php echo e($photo); ?>', 'Contoh foto <?php echo e($room['name']); ?>')">
                                                            <img src="<?php echo e($photo); ?>"
                                                                 alt="Contoh foto <?php echo e($room['name']); ?>"
                                                                 class="w-full h-16 object-cover rounded-md group-hover/photo:scale-110 transition-transform duration-300">
                                                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover/photo:bg-opacity-30 transition-all duration-300 rounded-md flex items-center justify-center">
                                                                <svg class="w-4 h-4 text-white opacity-0 group-hover/photo:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                                </svg>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                                <p class="text-xs text-gray-500 text-center">✨ Hasil foto berkualitas tinggi dari studio ini</p>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <input type="radio" name="ruang_studio" value="<?php echo e($room['name']); ?>" class="hidden studio-radio">
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <?php $__errorArgs = ['ruang_studio'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-600 text-sm mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Step 2: Pilih Waktu -->
                    <div class="space-y-6" id="waktuSection" style="display: none;">
                        <div class="border-b border-gray-200 pb-4">
                            <h4 class="text-lg font-semibold text-gray-900 flex items-center">
                                <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">2</span>
                                Pilih Waktu
                            </h4>
                            <p class="text-gray-600 mt-1 ml-11">Pilih waktu yang sesuai dengan kebutuhan Anda</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Tanggal -->
                            <div class="space-y-2">
                                <label for="tanggal_booking" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Tanggal Diinginkan
                                </label>
                                <input type="date" id="tanggal_booking" name="tanggal_booking" value="<?php echo e(old('tanggal_booking')); ?>" required
                                       min="<?php echo e(date('Y-m-d', strtotime('+1 day'))); ?>"
                                       class="form-input w-full">
                                <?php $__errorArgs = ['tanggal_booking'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-600 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Jenis Acara -->
                            <div class="space-y-2">
                                <label for="jenis_acara" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                    Jenis Acara
                                </label>
                                <select id="jenis_acara" name="jenis_acara" required class="form-input w-full">
                                    <option value="">Pilih Jenis Acara</option>
                                    <option value="Portrait Session" <?php echo e(old('jenis_acara') == 'Portrait Session' ? 'selected' : ''); ?>>Portrait Session</option>
                                    <option value="Product Photography" <?php echo e(old('jenis_acara') == 'Product Photography' ? 'selected' : ''); ?>>Product Photography</option>
                                    <option value="Family Photo" <?php echo e(old('jenis_acara') == 'Family Photo' ? 'selected' : ''); ?>>Family Photo</option>
                                    <option value="Corporate Headshot" <?php echo e(old('jenis_acara') == 'Corporate Headshot' ? 'selected' : ''); ?>>Corporate Headshot</option>
                                    <option value="Fashion Shoot" <?php echo e(old('jenis_acara') == 'Fashion Shoot' ? 'selected' : ''); ?>>Fashion Shoot</option>
                                    <option value="Content Creation" <?php echo e(old('jenis_acara') == 'Content Creation' ? 'selected' : ''); ?>>Content Creation</option>
                                    <option value="Video Recording" <?php echo e(old('jenis_acara') == 'Video Recording' ? 'selected' : ''); ?>>Video Recording</option>
                                    <option value="Lainnya" <?php echo e(old('jenis_acara') == 'Lainnya' ? 'selected' : ''); ?>>Lainnya</option>
                                </select>
                                <?php $__errorArgs = ['jenis_acara'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-600 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Jam Mulai -->
                            <div class="space-y-2">
                                <label for="jam_mulai" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Jam Mulai
                                </label>
                                <select id="jam_mulai" name="jam_mulai" required class="form-input w-full">
                                    <option value="">Pilih Jam Mulai</option>
                                    <?php for($i = 8; $i <= 20; $i++): ?>
                                        <option value="<?php echo e(sprintf('%02d:00', $i)); ?>" <?php echo e(old('jam_mulai') == sprintf('%02d:00', $i) ? 'selected' : ''); ?>>
                                            <?php echo e(sprintf('%02d:00', $i)); ?>

                                        </option>
                                        <option value="<?php echo e(sprintf('%02d:30', $i)); ?>" <?php echo e(old('jam_mulai') == sprintf('%02d:30', $i) ? 'selected' : ''); ?>>
                                            <?php echo e(sprintf('%02d:30', $i)); ?>

                                        </option>
                                    <?php endfor; ?>
                                </select>
                                <?php $__errorArgs = ['jam_mulai'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-600 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Jam Selesai -->
                            <div class="space-y-2">
                                <label for="jam_selesai" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Jam Selesai
                                </label>
                                <select id="jam_selesai" name="jam_selesai" required class="form-input w-full">
                                    <option value="">Pilih Jam Selesai</option>
                                    <?php for($i = 9; $i <= 22; $i++): ?>
                                        <option value="<?php echo e(sprintf('%02d:00', $i)); ?>" <?php echo e(old('jam_selesai') == sprintf('%02d:00', $i) ? 'selected' : ''); ?>>
                                            <?php echo e(sprintf('%02d:00', $i)); ?>

                                        </option>
                                        <option value="<?php echo e(sprintf('%02d:30', $i)); ?>" <?php echo e(old('jam_selesai') == sprintf('%02d:30', $i) ? 'selected' : ''); ?>>
                                            <?php echo e(sprintf('%02d:30', $i)); ?>

                                        </option>
                                    <?php endfor; ?>
                                </select>
                                <?php $__errorArgs = ['jam_selesai'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-600 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Jumlah Peserta -->
                            <div class="space-y-2">
                                <label for="jumlah_peserta" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    Jumlah Peserta
                                </label>
                                <input type="number" id="jumlah_peserta" name="jumlah_peserta" value="<?php echo e(old('jumlah_peserta', 1)); ?>" required min="1" max="20"
                                       placeholder="Berapa orang yang akan ikut sesi foto?"
                                       class="form-input w-full">
                                <?php $__errorArgs = ['jumlah_peserta'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-600 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Estimasi Biaya -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4" id="estimasiBiaya" style="display: none;">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="font-semibold text-blue-800">Estimasi Biaya</h5>
                                    <p class="text-sm text-blue-600" id="detailBiaya">-</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-2xl font-bold text-blue-600" id="totalBiaya">Rp 0</p>
                                    <p class="text-xs text-blue-600">*Belum termasuk pajak</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Data Diri -->
                    <div class="space-y-6" id="dataDiriSection" style="display: none;">
                        <div class="border-b border-gray-200 pb-4">
                            <h4 class="text-lg font-semibold text-gray-900 flex items-center">
                                <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">3</span>
                                Data Diri
                            </h4>
                            <p class="text-gray-600 mt-1 ml-11">Isi data diri Anda untuk booking studio</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Nama -->
                            <div class="space-y-2">
                                <label for="nama_pelanggan" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    Nama Lengkap
                                </label>
                                <input type="text" id="nama_pelanggan" name="nama_pelanggan" value="<?php echo e(old('nama_pelanggan')); ?>" required
                                       placeholder="Masukkan nama lengkap Anda"
                                       class="form-input w-full">
                                <?php $__errorArgs = ['nama_pelanggan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-600 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Email -->
                            <div class="space-y-2">
                                <label for="email_pelanggan" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                    </svg>
                                    Email
                                </label>
                                <input type="email" id="email_pelanggan" name="email_pelanggan" value="<?php echo e(old('email_pelanggan')); ?>" required
                                       placeholder="<EMAIL>"
                                       class="form-input w-full">
                                <?php $__errorArgs = ['email_pelanggan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-600 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Kontak -->
                            <div class="space-y-2">
                                <label for="kontak_pelanggan" class="form-label">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                    Nomor WhatsApp
                                </label>
                                <input type="text" id="kontak_pelanggan" name="kontak_pelanggan" value="<?php echo e(old('kontak_pelanggan')); ?>" required
                                       placeholder="08123456789"
                                       class="form-input w-full">
                                <?php $__errorArgs = ['kontak_pelanggan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-600 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Alamat -->
                        <div class="space-y-2">
                            <label for="alamat_pelanggan" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Alamat Lengkap
                            </label>
                            <textarea id="alamat_pelanggan" name="alamat_pelanggan" rows="3" required
                                      placeholder="Masukkan alamat lengkap Anda"
                                      class="form-input w-full resize-none"><?php echo e(old('alamat_pelanggan')); ?></textarea>
                            <?php $__errorArgs = ['alamat_pelanggan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-600 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Catatan -->
                        <div class="space-y-2">
                            <label for="catatan" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Catatan Khusus (Opsional)
                            </label>
                            <textarea id="catatan" name="catatan" rows="3"
                                      placeholder="Tambahkan catatan atau permintaan khusus untuk booking studio..."
                                      class="form-input w-full resize-none"><?php echo e(old('catatan')); ?></textarea>
                            <?php $__errorArgs = ['catatan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-600 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200" id="actionButtons" style="display: none;">
                        <a href="<?php echo e(route('user.bookings.index')); ?>"
                           class="btn btn-secondary px-8 py-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Batal
                        </a>
                        <button type="submit" class="btn btn-primary px-8 py-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Booking Sekarang
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Photo Modal -->
    <div id="photoModal" class="fixed inset-0 bg-black bg-opacity-75 z-50 hidden flex items-center justify-center p-4">
        <div class="relative max-w-4xl max-h-full">
            <img id="modalImage" src="" alt="" class="max-w-full max-h-full object-contain rounded-lg">
            <button onclick="closePhotoModal()" class="absolute top-4 right-4 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all duration-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    </div>

    <script>
        let selectedStudio = null;
        let studioPrice = 0;

        // Photo modal functions
        function openPhotoModal(imageSrc, altText) {
            const modal = document.getElementById('photoModal');
            const modalImage = document.getElementById('modalImage');
            modalImage.src = imageSrc;
            modalImage.alt = altText;
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closePhotoModal() {
            const modal = document.getElementById('photoModal');
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside
        document.getElementById('photoModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePhotoModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePhotoModal();
            }
        });

        // Studio selection
        document.querySelectorAll('.studio-card').forEach(card => {
            card.addEventListener('click', function() {
                // Remove previous selection
                document.querySelectorAll('.studio-card').forEach(c => {
                    c.classList.remove('border-blue-500', 'bg-blue-50');
                    c.querySelector('.studio-radio').checked = false;
                });

                // Select current
                this.classList.add('border-blue-500', 'bg-blue-50');
                this.querySelector('.studio-radio').checked = true;

                selectedStudio = this.dataset.studio;
                studioPrice = parseInt(this.dataset.price);

                showWaktuSection();
            });
        });



        function showWaktuSection() {
            document.getElementById('waktuSection').style.display = 'block';
            document.getElementById('waktuSection').scrollIntoView({ behavior: 'smooth' });
        }

        function showDataDiriSection() {
            document.getElementById('dataDiriSection').style.display = 'block';
            document.getElementById('dataDiriSection').scrollIntoView({ behavior: 'smooth' });
        }

        function showActionButtons() {
            document.getElementById('actionButtons').style.display = 'flex';
        }

        // Calculate cost when time changes
        function calculateCost() {
            const jamMulai = document.getElementById('jam_mulai').value;
            const jamSelesai = document.getElementById('jam_selesai').value;

            if (jamMulai && jamSelesai && selectedStudio) {
                const startTime = new Date('2000-01-01 ' + jamMulai);
                const endTime = new Date('2000-01-01 ' + jamSelesai);
                const duration = (endTime - startTime) / (1000 * 60 * 60); // hours

                if (duration > 0) {
                    const totalCost = duration * studioPrice;

                    document.getElementById('detailBiaya').textContent =
                        `${selectedStudio} × ${duration} jam × Rp ${studioPrice.toLocaleString('id-ID')}`;
                    document.getElementById('totalBiaya').textContent =
                        'Rp ' + totalCost.toLocaleString('id-ID');
                    document.getElementById('estimasiBiaya').style.display = 'block';

                    // Check if all required fields are filled to show data diri section
                    const requiredFields = ['tanggal_booking', 'jenis_acara', 'jam_mulai', 'jam_selesai', 'jumlah_peserta'];
                    const allFilled = requiredFields.every(field => document.getElementById(field).value.trim() !== '');

                    if (allFilled) {
                        showDataDiriSection();
                    }
                }
            }
        }

        // Add event listeners for time calculation
        document.getElementById('jam_mulai').addEventListener('change', calculateCost);
        document.getElementById('jam_selesai').addEventListener('change', calculateCost);
        document.getElementById('tanggal_booking').addEventListener('change', calculateCost);
        document.getElementById('jenis_acara').addEventListener('change', calculateCost);
        document.getElementById('jumlah_peserta').addEventListener('change', calculateCost);

        // Form validation and show action buttons
        document.getElementById('bookingForm').addEventListener('input', function() {
            // Show action buttons if all required fields are filled
            const requiredFields = ['nama_pelanggan', 'email_pelanggan', 'kontak_pelanggan', 'alamat_pelanggan', 'tanggal_booking', 'jenis_acara', 'jam_mulai', 'jam_selesai', 'jumlah_peserta'];
            const allFilled = requiredFields.every(field => document.getElementById(field).value.trim() !== '');

            if (allFilled && selectedStudio) {
                showActionButtons();
            }
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14)): ?>
<?php $attributes = $__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14; ?>
<?php unset($__attributesOriginalb94ad9089e1cdf6b2c00a7f1534f2d14); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14)): ?>
<?php $component = $__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14; ?>
<?php unset($__componentOriginalb94ad9089e1cdf6b2c00a7f1534f2d14); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\TokoPhotoStudio\resources\views/user/bookings/create.blade.php ENDPATH**/ ?>