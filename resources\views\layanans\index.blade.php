<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('<PERSON><PERSON><PERSON>') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-medium">Daftar Layanan</h3>
                        <a href="{{ route('layanans.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Tambah Layanan
                        </a>
                    </div>

                    <!-- Search Form -->
                    <div class="mb-6">
                        <form method="GET" action="{{ route('layanans.index') }}" class="flex gap-4">
                            <input type="text" name="search" value="{{ request('search') }}" 
                                   placeholder="Cari layanan..." 
                                   class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Cari
                            </button>
                        </form>
                    </div>

                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            {{ session('success') }}
                        </div>
                    @endif

                    <!-- Services Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @forelse($layanans as $layanan)
                            <div class="bg-white border border-gray-200 rounded-lg shadow-md overflow-hidden">
                                @if($layanan->gambar_layanan)
                                    <img src="{{ asset('storage/' . $layanan->gambar_layanan) }}" 
                                         alt="{{ $layanan->nama_layanan }}"
                                         class="w-full h-48 object-cover">
                                @else
                                    <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                @endif
                                
                                <div class="p-4">
                                    <div class="flex justify-between items-start mb-2">
                                        <h4 class="text-lg font-semibold text-gray-900">{{ $layanan->nama_layanan }}</h4>
                                        <span class="px-2 py-1 text-xs font-medium rounded-full 
                                            @if($layanan->aktif) bg-green-100 text-green-800 @else bg-red-100 text-red-800 @endif">
                                            {{ $layanan->aktif ? 'Aktif' : 'Nonaktif' }}
                                        </span>
                                    </div>
                                    
                                    <p class="text-sm text-gray-600 mb-2">{{ ucfirst($layanan->kategori) }}</p>
                                    <p class="text-sm text-gray-700 mb-3 line-clamp-2">{{ Str::limit($layanan->deskripsi, 100) }}</p>
                                    
                                    <div class="mb-3">
                                        <p class="text-lg font-bold text-blue-600">{{ $layanan->harga_format }}</p>
                                        @if($layanan->harga_premium)
                                            <p class="text-sm text-gray-600">Premium: {{ $layanan->harga_premium_format }}</p>
                                        @endif
                                        <p class="text-xs text-gray-500">{{ $layanan->durasi_jam }} jam</p>
                                    </div>
                                    
                                    <div class="flex justify-between items-center">
                                        <div class="flex space-x-2">
                                            <a href="{{ route('layanans.show', $layanan) }}" 
                                               class="text-blue-600 hover:text-blue-900 text-sm">Lihat</a>
                                            <a href="{{ route('layanans.edit', $layanan) }}" 
                                               class="text-yellow-600 hover:text-yellow-900 text-sm">Edit</a>
                                        </div>
                                        <form action="{{ route('layanans.destroy', $layanan) }}" method="POST" class="inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" 
                                                    onclick="return confirm('Yakin ingin menghapus layanan ini?')"
                                                    class="text-red-600 hover:text-red-900 text-sm">Hapus</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="col-span-full text-center py-8">
                                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Belum ada layanan</h3>
                                <p class="text-gray-600 mb-4">Mulai dengan menambahkan layanan pertama.</p>
                                <a href="{{ route('layanans.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Tambah Layanan
                                </a>
                            </div>
                        @endforelse
                    </div>

                    <!-- Pagination -->
                    @if($layanans->hasPages())
                        <div class="mt-6">
                            {{ $layanans->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
