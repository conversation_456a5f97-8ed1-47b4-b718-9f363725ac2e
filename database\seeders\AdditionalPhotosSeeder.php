<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Multimedia;
use App\Models\Pesanan;
use Illuminate\Support\Facades\Storage;

class AdditionalPhotosSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Pastikan folder multimedia ada
        if (!Storage::disk('public')->exists('multimedia')) {
            Storage::disk('public')->makeDirectory('multimedia');
        }

        // Ambil pesanan yang ada
        $pesanans = Pesanan::all();
        if ($pesanans->count() == 0) {
            $this->command->info('Tidak ada pesanan. Additional photos seeder dilewati.');
            return;
        }

        // Data foto tambahan yang cantik
        $additionalPhotos = [
            [
                'pesanan_id' => $pesanans->first()->id,
                'nama_file' => 'Graduation Ceremony Moment',
                'path_file' => 'multimedia/graduation_ceremony.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 1920000,
                'kategori' => 'hasil_edit',
                'deskripsi' => 'Momen wisuda yang bersejarah dengan toga dan topi wisuda yang membanggakan.',
                'url_source' => 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=800&h=600&fit=crop'
            ],
            [
                'pesanan_id' => $pesanans->count() > 1 ? $pesanans->skip(1)->first()->id : $pesanans->first()->id,
                'nama_file' => 'Baby Newborn Portrait',
                'path_file' => 'multimedia/baby_newborn.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 1664000,
                'kategori' => 'preview',
                'deskripsi' => 'Foto bayi yang menggemaskan dengan pose natural. Momen precious yang akan dikenang selamanya.',
                'url_source' => 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=800&h=600&fit=crop'
            ],
            [
                'pesanan_id' => $pesanans->first()->id,
                'nama_file' => 'Corporate Event Documentation',
                'path_file' => 'multimedia/corporate_event.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 2432000,
                'kategori' => 'hasil_edit',
                'deskripsi' => 'Dokumentasi acara korporat dengan suasana profesional dan dinamis.',
                'url_source' => 'https://images.unsplash.com/photo-1511578314322-379afb476865?w=800&h=600&fit=crop'
            ],
            [
                'pesanan_id' => $pesanans->count() > 1 ? $pesanans->skip(1)->first()->id : $pesanans->first()->id,
                'nama_file' => 'Nature Landscape Photography',
                'path_file' => 'multimedia/nature_landscape.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 2688000,
                'kategori' => 'raw',
                'deskripsi' => 'Foto landscape alam yang menakjubkan dengan pemandangan gunung dan langit yang dramatis.',
                'url_source' => 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop'
            ],
            [
                'pesanan_id' => $pesanans->first()->id,
                'nama_file' => 'Couple Engagement Session',
                'path_file' => 'multimedia/couple_engagement.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 2048000,
                'kategori' => 'preview',
                'deskripsi' => 'Sesi foto engagement yang romantis dengan pasangan yang bahagia dan penuh cinta.',
                'url_source' => 'https://images.unsplash.com/photo-1516589178581-6cd7833ae3b2?w=800&h=600&fit=crop'
            ],
            [
                'pesanan_id' => $pesanans->count() > 1 ? $pesanans->skip(1)->first()->id : $pesanans->first()->id,
                'nama_file' => 'Business Portrait Executive',
                'path_file' => 'multimedia/business_portrait.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 1792000,
                'kategori' => 'hasil_edit',
                'deskripsi' => 'Portrait bisnis eksekutif dengan pencahayaan profesional dan background yang elegan.',
                'url_source' => 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=800&h=600&fit=crop'
            ],
            [
                'pesanan_id' => $pesanans->first()->id,
                'nama_file' => 'Children Playing Candid',
                'path_file' => 'multimedia/children_playing.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 1856000,
                'kategori' => 'preview',
                'deskripsi' => 'Foto candid anak-anak yang sedang bermain dengan ekspresi gembira dan natural.',
                'url_source' => 'https://images.unsplash.com/photo-1503454537195-1dcabb73ffb9?w=800&h=600&fit=crop'
            ],
            [
                'pesanan_id' => $pesanans->count() > 1 ? $pesanans->skip(1)->first()->id : $pesanans->first()->id,
                'nama_file' => 'Food Photography Artistic',
                'path_file' => 'multimedia/food_photography.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 1536000,
                'kategori' => 'hasil_edit',
                'deskripsi' => 'Foto makanan yang artistik dengan styling yang menarik dan pencahayaan yang sempurna.',
                'url_source' => 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop'
            ]
        ];

        foreach ($additionalPhotos as $data) {
            // Download dan simpan foto dari Unsplash
            if (isset($data['url_source'])) {
                try {
                    $imageContent = file_get_contents($data['url_source']);
                    if ($imageContent !== false) {
                        Storage::disk('public')->put($data['path_file'], $imageContent);
                        $this->command->info("Downloaded: {$data['nama_file']}");
                    }
                } catch (\Exception $e) {
                    $this->command->warn("Failed to download: {$data['nama_file']}");
                }
                unset($data['url_source']);
            }

            Multimedia::create($data);
        }

        $this->command->info('Additional photos seeder completed! Added ' . count($additionalPhotos) . ' beautiful photos.');
    }
}
