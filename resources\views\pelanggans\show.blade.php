<x-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Detail Pelanggan') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold">Detail Pelanggan</h3>
                        <div class="flex gap-2">
                            <a href="{{ route('pelanggans.edit', $pelanggan) }}" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                                Edit
                            </a>
                            <a href="{{ route('pelanggans.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Kembali
                            </a>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-700 mb-3">Informasi Pelanggan</h4>
                            
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">ID</label>
                                    <p class="text-gray-900">{{ $pelanggan->id }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Nama</label>
                                    <p class="text-gray-900">{{ $pelanggan->nama }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Email</label>
                                    <p class="text-gray-900">{{ $pelanggan->email }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Kontak</label>
                                    <p class="text-gray-900">{{ $pelanggan->kontak }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Alamat</label>
                                    <p class="text-gray-900">{{ $pelanggan->alamat ?: 'Tidak ada alamat' }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">Tanggal Daftar</label>
                                    <p class="text-gray-900">{{ $pelanggan->created_at->format('d/m/Y H:i') }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-700 mb-3">Riwayat Transaksi</h4>
                            
                            @if($pelanggan->pesanans->count() > 0)
                                <div class="space-y-2">
                                    @foreach($pelanggan->pesanans->take(5) as $pesanan)
                                        <div class="bg-white p-3 rounded border">
                                            <p class="font-medium">{{ $pesanan->jenis_layanan }}</p>
                                            <p class="text-sm text-gray-600">{{ $pesanan->paket }} - Rp {{ number_format($pesanan->harga, 0, ',', '.') }}</p>
                                            <p class="text-xs text-gray-500">{{ $pesanan->tanggal_pesanan->format('d/m/Y') }}</p>
                                        </div>
                                    @endforeach
                                    
                                    @if($pelanggan->pesanans->count() > 5)
                                        <p class="text-sm text-gray-500">Dan {{ $pelanggan->pesanans->count() - 5 }} pesanan lainnya...</p>
                                    @endif
                                </div>
                            @else
                                <p class="text-gray-500">Belum ada riwayat pesanan</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-sidebar-layout>
