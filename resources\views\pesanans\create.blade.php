<x-sidebar-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Tambah Pesanan') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold">Form Tambah Pesanan</h3>
                        <a href="{{ route('pesanans.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                            Kembali
                        </a>
                    </div>

                    <form action="{{ route('pesanans.store') }}" method="POST">
                        @csrf
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Pelanggan -->
                            <div>
                                <x-input-label for="pelanggan_id" :value="__('Pelanggan')" />
                                <select id="pelanggan_id" name="pelanggan_id" 
                                        class="block mt-1 w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
                                    <option value="">Pilih Pelanggan</option>
                                    @foreach($pelanggans as $pelanggan)
                                        <option value="{{ $pelanggan->id }}" {{ old('pelanggan_id') == $pelanggan->id ? 'selected' : '' }}>
                                            {{ $pelanggan->nama }} - {{ $pelanggan->email }}
                                        </option>
                                    @endforeach
                                </select>
                                <x-input-error :messages="$errors->get('pelanggan_id')" class="mt-2" />
                            </div>

                            <!-- Jenis Layanan -->
                            <div>
                                <x-input-label for="jenis_layanan" :value="__('Jenis Layanan')" />
                                <select id="jenis_layanan" name="jenis_layanan" 
                                        class="block mt-1 w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
                                    <option value="">Pilih Jenis Layanan</option>
                                    <option value="Wedding Photography" {{ old('jenis_layanan') == 'Wedding Photography' ? 'selected' : '' }}>Wedding Photography</option>
                                    <option value="Portrait Photography" {{ old('jenis_layanan') == 'Portrait Photography' ? 'selected' : '' }}>Portrait Photography</option>
                                    <option value="Event Photography" {{ old('jenis_layanan') == 'Event Photography' ? 'selected' : '' }}>Event Photography</option>
                                    <option value="Product Photography" {{ old('jenis_layanan') == 'Product Photography' ? 'selected' : '' }}>Product Photography</option>
                                    <option value="Studio Photography" {{ old('jenis_layanan') == 'Studio Photography' ? 'selected' : '' }}>Studio Photography</option>
                                </select>
                                <x-input-error :messages="$errors->get('jenis_layanan')" class="mt-2" />
                            </div>

                            <!-- Paket -->
                            <div>
                                <x-input-label for="paket" :value="__('Paket')" />
                                <select id="paket" name="paket" 
                                        class="block mt-1 w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
                                    <option value="">Pilih Paket</option>
                                    <option value="Basic" {{ old('paket') == 'Basic' ? 'selected' : '' }}>Basic</option>
                                    <option value="Standard" {{ old('paket') == 'Standard' ? 'selected' : '' }}>Standard</option>
                                    <option value="Premium" {{ old('paket') == 'Premium' ? 'selected' : '' }}>Premium</option>
                                    <option value="Deluxe" {{ old('paket') == 'Deluxe' ? 'selected' : '' }}>Deluxe</option>
                                </select>
                                <x-input-error :messages="$errors->get('paket')" class="mt-2" />
                            </div>

                            <!-- Harga -->
                            <div>
                                <x-input-label for="harga" :value="__('Harga')" />
                                <x-text-input id="harga" class="block mt-1 w-full" type="number" name="harga" :value="old('harga')" required min="0" step="0.01" />
                                <x-input-error :messages="$errors->get('harga')" class="mt-2" />
                            </div>

                            <!-- Tanggal Pesanan -->
                            <div>
                                <x-input-label for="tanggal_pesanan" :value="__('Tanggal Pesanan')" />
                                <x-text-input id="tanggal_pesanan" class="block mt-1 w-full" type="date" name="tanggal_pesanan" :value="old('tanggal_pesanan')" required />
                                <x-input-error :messages="$errors->get('tanggal_pesanan')" class="mt-2" />
                            </div>

                            <!-- Status -->
                            <div>
                                <x-input-label for="status" :value="__('Status')" />
                                <select id="status" name="status" 
                                        class="block mt-1 w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
                                    <option value="pending" {{ old('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="proses" {{ old('status') == 'proses' ? 'selected' : '' }}>Proses</option>
                                    <option value="selesai" {{ old('status') == 'selesai' ? 'selected' : '' }}>Selesai</option>
                                    <option value="dibatalkan" {{ old('status') == 'dibatalkan' ? 'selected' : '' }}>Dibatalkan</option>
                                </select>
                                <x-input-error :messages="$errors->get('status')" class="mt-2" />
                            </div>
                        </div>

                        <!-- Catatan -->
                        <div class="mt-6">
                            <x-input-label for="catatan" :value="__('Catatan')" />
                            <textarea id="catatan" name="catatan" rows="3" 
                                      class="block mt-1 w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">{{ old('catatan') }}</textarea>
                            <x-input-error :messages="$errors->get('catatan')" class="mt-2" />
                        </div>

                        <div class="flex items-center justify-end mt-6">
                            <x-primary-button class="ml-4">
                                {{ __('Simpan') }}
                            </x-primary-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-sidebar-layout>
