<?php

namespace Database\Seeders;

use App\Models\Multimedia;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MultimediaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Pastikan folder multimedia ada
        if (!\Illuminate\Support\Facades\Storage::disk('public')->exists('multimedia')) {
            \Illuminate\Support\Facades\Storage::disk('public')->makeDirectory('multimedia');
        }

        // Ambil pesanan yang ada
        $pesanans = \App\Models\Pesanan::all();
        if ($pesanans->count() == 0) {
            $this->command->info('Tidak ada pesanan. Multimedia seeder dilewati.');
            return;
        }

        // Data multimedia contoh dengan foto cantik
        $multimediaData = [
            [
                'pesanan_id' => $pesanans->first()->id,
                'nama_file' => 'Wedding Portrait Beautiful Couple',
                'path_file' => 'multimedia/wedding_portrait_1.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 2048000,
                'kategori' => 'hasil_edit',
                'deskripsi' => 'Foto pernikahan yang romantis dengan pencahayaan natural yang indah.',
                'url_source' => 'https://images.unsplash.com/photo-1519741497674-611481863552?w=800&h=600&fit=crop'
            ],
            [
                'pesanan_id' => $pesanans->first()->id,
                'nama_file' => 'Elegant Bridal Portrait',
                'path_file' => 'multimedia/bridal_portrait_1.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 1856000,
                'kategori' => 'preview',
                'deskripsi' => 'Portrait pengantin wanita yang elegan dengan gaun putih yang memukau.',
                'url_source' => 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=800&h=600&fit=crop'
            ],
            [
                'pesanan_id' => $pesanans->count() > 1 ? $pesanans->skip(1)->first()->id : $pesanans->first()->id,
                'nama_file' => 'Family Portrait Outdoor',
                'path_file' => 'multimedia/family_portrait_1.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 2304000,
                'kategori' => 'hasil_edit',
                'deskripsi' => 'Foto keluarga bahagia di taman dengan latar belakang alam yang indah.',
                'url_source' => 'https://images.unsplash.com/photo-1511895426328-dc8714191300?w=800&h=600&fit=crop'
            ],
            [
                'pesanan_id' => $pesanans->count() > 1 ? $pesanans->skip(1)->first()->id : $pesanans->first()->id,
                'nama_file' => 'Professional Headshot',
                'path_file' => 'multimedia/headshot_professional_1.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 1536000,
                'kategori' => 'preview',
                'deskripsi' => 'Foto profesi yang profesional dengan pencahayaan studio yang sempurna.',
                'url_source' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop'
            ],
            [
                'pesanan_id' => $pesanans->first()->id,
                'nama_file' => 'Fashion Portrait Model',
                'path_file' => 'multimedia/fashion_portrait_1.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 2176000,
                'kategori' => 'raw',
                'deskripsi' => 'Foto fashion dengan model yang menawan. Gaya modern dengan komposisi yang artistik.',
                'url_source' => 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=800&h=600&fit=crop'
            ],
            [
                'pesanan_id' => $pesanans->first()->id,
                'nama_file' => 'Graduation Ceremony Moment',
                'path_file' => 'multimedia/graduation_ceremony.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 1920000,
                'kategori' => 'hasil_edit',
                'deskripsi' => 'Momen wisuda yang bersejarah dengan toga dan topi wisuda yang membanggakan.',
                'url_source' => 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=800&h=600&fit=crop'
            ],
            [
                'pesanan_id' => $pesanans->count() > 1 ? $pesanans->skip(1)->first()->id : $pesanans->first()->id,
                'nama_file' => 'Baby Newborn Portrait',
                'path_file' => 'multimedia/baby_newborn.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 1664000,
                'kategori' => 'preview',
                'deskripsi' => 'Foto bayi yang menggemaskan dengan pose natural. Momen precious yang akan dikenang selamanya.',
                'url_source' => 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=800&h=600&fit=crop'
            ],
            [
                'pesanan_id' => $pesanans->first()->id,
                'nama_file' => 'Corporate Event Documentation',
                'path_file' => 'multimedia/corporate_event.jpg',
                'tipe_file' => 'image/jpeg',
                'ukuran_file' => 2432000,
                'kategori' => 'hasil_edit',
                'deskripsi' => 'Dokumentasi acara korporat dengan suasana profesional dan dinamis.',
                'url_source' => 'https://images.unsplash.com/photo-1511578314322-379afb476865?w=800&h=600&fit=crop'
            ]
        ];

        foreach ($multimediaData as $data) {
            // Download dan simpan foto dari Unsplash
            if (isset($data['url_source'])) {
                try {
                    $imageContent = file_get_contents($data['url_source']);
                    if ($imageContent !== false) {
                        \Illuminate\Support\Facades\Storage::disk('public')->put($data['path_file'], $imageContent);
                        $this->command->info("Downloaded: {$data['nama_file']}");
                    }
                } catch (\Exception $e) {
                    $this->command->warn("Failed to download: {$data['nama_file']}");
                }
                unset($data['url_source']);
            }

            Multimedia::create($data);
        }

        $this->command->info('Multimedia seeder completed! Added beautiful sample photos.');
    }
}
