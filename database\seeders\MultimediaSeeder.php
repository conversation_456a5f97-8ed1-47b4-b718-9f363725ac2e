<?php

namespace Database\Seeders;

use App\Models\Multimedia;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MultimediaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if multimedia data already exists
        if (Multimedia::count() > 0) {
            return;
        }

        // Seed sample multimedia files
        Multimedia::create([
            'pesanan_id' => 1,
            'nama_file' => 'wedding_preview_001.jpg',
            'path_file' => 'multimedia/wedding_preview_001.jpg',
            'tipe_file' => 'image/jpeg',
            'ukuran_file' => 2048000, // 2MB
            'kategori' => 'preview',
            'deskripsi' => 'Preview foto wedding couple'
        ]);

        Multimedia::create([
            'pesanan_id' => 1,
            'nama_file' => 'wedding_edited_final.jpg',
            'path_file' => 'multimedia/wedding_edited_final.jpg',
            'tipe_file' => 'image/jpeg',
            'ukuran_file' => 5120000, // 5MB
            'kategori' => 'hasil_edit',
            'deskripsi' => 'Hasil edit final foto wedding'
        ]);

        Multimedia::create([
            'pesanan_id' => 2,
            'nama_file' => 'portrait_raw_001.jpg',
            'path_file' => 'multimedia/portrait_raw_001.jpg',
            'tipe_file' => 'image/jpeg',
            'ukuran_file' => 8192000, // 8MB
            'kategori' => 'raw',
            'deskripsi' => 'File RAW untuk portrait session'
        ]);

        Multimedia::create([
            'pesanan_id' => 2,
            'nama_file' => 'portrait_preview.jpg',
            'path_file' => 'multimedia/portrait_preview.jpg',
            'tipe_file' => 'image/jpeg',
            'ukuran_file' => 1536000, // 1.5MB
            'kategori' => 'preview',
            'deskripsi' => 'Preview foto portrait untuk review client'
        ]);
    }
}
