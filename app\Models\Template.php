<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Template extends Model
{
    use HasFactory;

    protected $fillable = [
        'nama_template',
        'deskripsi',
        'kategori',
        'preset_settings',
        'thumbnail_path',
        'status',
    ];

    protected $casts = [
        'preset_settings' => 'array',
    ];

    // Accessor for thumbnail URL
    public function getThumbnailUrlAttribute()
    {
        return $this->thumbnail_path ? asset('storage/' . $this->thumbnail_path) : null;
    }
}
