<?php

namespace App\Http\Controllers;

use App\Models\Pelanggan;
use App\Models\Pesanan;
use App\Models\Pembayaran;
use App\Models\Booking;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function admin()
    {
        $totalPelanggan = Pelanggan::count();
        $totalPesanan = Pesanan::count();
        $totalPendapatan = Pembayaran::where('status', 'lunas')->sum('jumlah_bayar');
        $bookingHariIni = Booking::whereDate('tanggal_booking', today())->count();

        return view('dashboard.admin', compact(
            'totalPelanggan',
            'totalPesanan',
            'totalPendapatan',
            'bookingHariIni'
        ));
    }

    public function user()
    {
        return view('dashboard.user');
    }
}
